package lk.sout.business.reservation.service.impl;

import lk.sout.business.inventory.entity.Item;
import lk.sout.business.inventory.service.ItemService;
import lk.sout.business.trade.service.CashDrawerService;
import lk.sout.business.trade.service.SalesInvoiceService;
import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Response;
import lk.sout.core.entity.Sequence;
import lk.sout.core.entity.User;
import lk.sout.core.service.MetaDataService;
import lk.sout.core.service.SequenceService;
import lk.sout.core.service.UserService;
import lk.sout.business.inventory.entity.Stock;
import lk.sout.business.inventory.service.StockService;
import lk.sout.business.reservation.entity.BoardType;
import lk.sout.business.reservation.entity.Reservation;
import lk.sout.business.reservation.repository.ReservationRepository;
import lk.sout.business.reservation.repository.RoomRepository;
import lk.sout.business.reservation.service.BoardTypeService;
import lk.sout.business.reservation.service.ReservationService;
import lk.sout.business.trade.entity.SalesInvoiceRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class ReservationServiceImpl implements ReservationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReservationServiceImpl.class);

    @Autowired
    ReservationRepository reservationRepository;

    @Autowired
    SequenceService sequenceService;

    @Autowired
    UserService userService;

    @Autowired
    StockService stockService;

    @Autowired
    MetaDataService metaDataService;

    @Autowired
    ItemService itemService;

    @Autowired
    BoardTypeService boardTypeService;

    @Autowired
    Response response;

    @Override
    public Response saveReservation(Reservation reservation) {
        try {
            String seqId = "";
            Sequence sequence = sequenceService.findSequenceByName("Reservation Code");
            seqId = (sequence.getPrefix() + String.valueOf(sequence.getCounter() + 1));
            reservation.setReservationCode(seqId);
            sequence = null;

            BoardType boardtype = boardTypeService.findById(reservation.getBoardType().getId());
            int dates = reservation.getFrom().compareTo(reservation.getTo());
            reservation.setRoomCharge(boardtype.getCharge() * (dates * -1));
            reservation.setTodayBook(true);
            reservation.setReservationStatus(metaDataService.searchMetaData("Booked", "ReservationStatus"));
            sequenceService.incrementSequence("Reservation Code");
            reservationRepository.save(reservation);
            response.setCode(200);
            response.setMessage("Reservation Created Successfully");
        } catch (Exception e) {
            LOGGER.error("Reservation Category Failed", e.getMessage());
            response.setCode(501);
            response.setMessage("Reservation Creating Failed");
            response.setData(e.getMessage());
        }
        return response;
    }

    @Override
    @Transactional
    public Response saveInvoice(Reservation reservationInvoice) {
        try {
            User user = userService.findUser();

            Reservation reservation = reservationRepository.findByIdAndReservationCode(reservationInvoice.getId(),
                    reservationInvoice.getReservationCode());

            String seqId = "";
            Sequence sequence = sequenceService.findSequenceByName("Reservation Invoice");
            seqId = (sequence.getPrefix() + String.valueOf(sequence.getCounter() + 1));
            reservation.setInvoiceNo(seqId);
            sequence = null;

            reservation.setInvoiceDate(LocalDateTime.now());

            if (reservationInvoice.getTotalAmount().compareTo(reservationInvoice.getPayment()) <= 0) {
                reservationInvoice.setInvoiceStatus(metaDataService.searchMetaData("Paid", "PaymentStatus"));
            } else if (reservationInvoice.getPayment() == 0) {
                reservationInvoice.setInvoiceStatus(metaDataService.searchMetaData("Pending", "PaymentStatus"));
            } else if (reservationInvoice.getTotalAmount().compareTo(reservationInvoice.getPayment()) > 0 &&
                    reservationInvoice.getPayment() > 0) {
                reservationInvoice.setInvoiceStatus(metaDataService.searchMetaData("Partially Paid", "PaymentStatus"));
            }

            reservation.setDueDate(reservationInvoice.getDueDate());
            reservation.setPayment(reservationInvoice.getPayment());
            reservation.setPrice(reservationInvoice.getPrice());
            reservation.setServiceCharge(reservationInvoice.getServiceCharge());
            reservation.setSubTotal(reservationInvoice.getSubTotal());
            reservation.setTotalAmount(reservationInvoice.getTotalAmount());
            reservation.setTotalDiscount(reservationInvoice.getTotalDiscount());
            reservation.setSalesInvoiceRecords(reservationInvoice.getSalesInvoiceRecords());

            for (SalesInvoiceRecord salesInvoiceRecord : reservation.getSalesInvoiceRecords()) {
                salesInvoiceRecord.setDate(LocalDate.now());
                Item salesItem = itemService.findOneByItemCode(salesInvoiceRecord.getItemCode());
                if (salesItem.isManageStock()) {
                    Stock stock = null;

                    // Use stockId if provided from frontend for direct lookup
                    if (salesInvoiceRecord.getStockId() != null && !salesInvoiceRecord.getStockId().isEmpty()) {
                        stock = stockService.findById(salesInvoiceRecord.getStockId());
                        if (stock == null) {
                            throw new RuntimeException(String.format("Stock record not found for stockId: %s, item: %s (%s)",
                                    salesInvoiceRecord.getStockId(), salesItem.getItemName(), salesItem.getBarcode()));
                        }
                    } else {
                        // Fallback to barcode + warehouse lookup (legacy support)
                        List<Stock> stockList = stockService.findByBarcodeAndWarehouse(salesItem.getBarcode(), user.getWarehouseCode());
                        if (!stockList.isEmpty()) {
                            // Use the first stock record with sufficient quantity
                            for (Stock s : stockList) {
                                if (s.getQuantity() >= salesInvoiceRecord.getQuantity()) {
                                    stock = s;
                                    break;
                                }
                            }
                            // If no stock with sufficient quantity, use the first one (will fail later with proper error)
                            if (stock == null) {
                                stock = stockList.get(0);
                            }
                        }
                    }

                    if (stock != null) {
                        // Check if sufficient stock is available
                        if (stock.getQuantity() >= salesInvoiceRecord.getQuantity()) {
                            stock.setQuantity(stock.getQuantity() - salesInvoiceRecord.getQuantity());
                            stockService.save(stock, "Updating Stock by Reservation Invoice", salesItem.getItemCode());
                        } else {
                            throw new RuntimeException(String.format("Insufficient stock for item: %s (%s). Required: %.2f, Available: %.2f",
                                    salesItem.getItemName(), salesItem.getBarcode(), salesInvoiceRecord.getQuantity(), stock.getQuantity()));
                        }
                    } else {
                        throw new RuntimeException(String.format("Stock not found for item: %s (%s) in warehouse: %d",
                                salesItem.getItemName(), salesItem.getBarcode(), user.getWarehouseCode()));
                    }
                }
            }

            reservation.setCounterNo(user.getCashDrawerNo());

            sequenceService.incrementSequence("Reservation Invoice");
            reservation.setReservationStatus(metaDataService.searchMetaData("Checked", "ReservationStatus"));
            reservationRepository.save(reservation);
            response.setCode(200);
            response.setData(reservation.getInvoiceNo());
            response.setMessage("Reservation Invoice Successfully saved");
            return response;
        } catch (Exception e) {
            LOGGER.error("Reservation Invoice Saving Failed" + e.getMessage());
            response.setCode(501);
            response.setMessage("Reservation Invoice Saving Failed");
            response.setData(e.getMessage());
            return response;
        }
    }

    @Override
    public List<Reservation> findAllPendingReservations() {
        try {
            MetaData booked = metaDataService.searchMetaData("Booked", "ReservationStatus");
            List<Reservation> reservations = reservationRepository.findAllByReservationStatusId(booked.getId());
            return reservations;
        } catch (Exception e) {
            LOGGER.error("Reservation List Retrieving Failed");
            return null;
        }
    }

    @Override
    public Iterable<Reservation> findAll(Pageable pageable) {
        try {
            Iterable<Reservation> r = reservationRepository.findAll(pageable);
            return r;
        } catch (Exception ex) {
            LOGGER.error("Find All Reservation Failed", ex.getMessage());
            return null;
        }
    }

    @Override
    public Reservation findOne(String id) {
        try {
            Optional<Reservation> reservation = reservationRepository.findById(id);
            return reservation.get();
        } catch (Exception ex) {
            LOGGER.error("Retrieving an Reservation failed : " + ex.getMessage());
            return null;
        }
    }


    @Override
    public Integer getAllCount() {
        try {
            Integer count = Math.toIntExact(reservationRepository.count());
            return count;
        } catch (Exception ex) {
            LOGGER.error("Retrieving an Item failed : " + ex.getMessage());
            return null;
        }
    }

    @Override
    public boolean checkFromDate(String roomId, LocalDate from) {
        try {
            Reservation r = reservationRepository.findByRoomAndFrom(roomId, from);
            if (null != r.getRoom().getId()) {
                if (null != r.getFrom()) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }

        } catch (Exception ex) {
            LOGGER.error("Check From Date Failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public List<Reservation> findAllBookedRoom(LocalDate today) {
        try {
            List<Reservation> reservations = reservationRepository.findAllByFrom(today);
            return reservations;
        } catch (Exception ex) {
            LOGGER.error("Check From Date Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Reservation findById(String id) {
        try {
            Reservation r = reservationRepository.findAllById(id);
            return r;
        } catch (Exception ex) {
            LOGGER.error("Retrieving Reservation failed : " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Reservation> findAllByStatusLikeIgnoreCase(String booked) {
        try {
            List<Reservation> r = reservationRepository.findAllByReservationStatusLikeIgnoreCase(booked);
            return r;
        } catch (Exception ex) {
            LOGGER.error("Retrieving Reservation failed : " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Reservation> findReservationByRoomNo(String id, String status) {
        try {
            List<Reservation> res = reservationRepository.findAllByRoom_IdAndReservationStatus(id, status);
            return res;
        } catch (Exception ex) {
            LOGGER.error("Returning Room No Failed : " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Reservation findByIdAndRoomId(String reservationId, String roomId) {
        try {
            Reservation r = reservationRepository.findByIdAndRoomId(reservationId, roomId);
            return r;
        } catch (Exception ex) {
            LOGGER.error("Returning reservation failed : " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Reservation findReservationInvoiceByInvNo(String invoiceNo) {
        try {
            Reservation reservation = reservationRepository.findByInvoiceNo(invoiceNo);
            return reservation;
        } catch (Exception e) {
            LOGGER.error(e + "Reservation Retrieving failed");
            return null;
        }
    }

}