package lk.sout.business.restaurent.repository;

import lk.sout.business.restaurent.entity.Order;
import lk.sout.business.restaurent.entity.Table;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface OrderRepository extends MongoRepository<Order, String> {

    List<Order> findTop15ByOrderByIdDesc();

    List<Order> findAllByTable(Table table);

    List<Order> findAllByOrderNoLike(String s);

    Order findByOrderNo(String s);

    List<Order> findByDateBetween(LocalDate s, LocalDate date);

    List<Order> findByActive(boolean active);
}