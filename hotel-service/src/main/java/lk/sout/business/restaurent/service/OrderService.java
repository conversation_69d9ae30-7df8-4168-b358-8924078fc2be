package lk.sout.business.restaurent.service;

import lk.sout.business.restaurent.entity.Table;
import lk.sout.core.entity.Response;
import lk.sout.business.restaurent.entity.Order;

import java.time.LocalDate;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 2/9/2022
 */
public interface OrderService {

    Response save(Order order);

    List<Order> findTop15ByOrderByIdDesc();

    List<Order> findAll();
    
    List<Order> findActive();

    List<Order> findByTableNo(String tableNo);

    Order findByOrderNo(String s);

    Order findById(String id);

    List<Order> findByOrderNoLike(String s);

    List<Order> findByDate(LocalDate date);

    boolean deleteRecord(Order order);
}