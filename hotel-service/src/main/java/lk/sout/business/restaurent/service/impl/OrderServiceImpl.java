package lk.sout.business.restaurent.service.impl;

import lk.sout.business.restaurent.entity.Table;
import lk.sout.business.restaurent.service.TableService;
import lk.sout.core.entity.Response;
import lk.sout.core.entity.Sequence;
import lk.sout.core.service.MetaDataService;
import lk.sout.core.service.SequenceService;
import lk.sout.business.restaurent.entity.Order;
import lk.sout.business.trade.entity.SalesInvoiceRecord;
import lk.sout.business.restaurent.repository.OrderRepository;
import lk.sout.business.restaurent.repository.template.OrderRepositoryTemplate;
import lk.sout.business.restaurent.service.OrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.time.LocalDate;
import java.util.List;

@Service
public class OrderServiceImpl implements OrderService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderServiceImpl.class);

    @Autowired
    OrderRepository orderRepository;

    @Autowired
    OrderRepositoryTemplate orderRepositoryTemplate;

    @Autowired
    SequenceService sequenceService;

    @Autowired
    TableService tableService;

    @Autowired
    Response response;

    @Autowired
    Sequence sequence;

    @Autowired
    MetaDataService metaDataService;

    @Override
    @Transactional
    public Response save(Order order) {
        try {
            Order si = null;
            // Validate table number is provided
            if (order.getTableNo() == null || order.getTableNo().trim().isEmpty()) {
                response.setCode(400);
                response.setMessage("Table number is required to save an order.");
                response.setSuccess(false);
                return response;
            }

            // Validate table exists
            Table table = tableService.findByTableNo(order.getTableNo());
            if (table == null) {
                response.setCode(404);
                response.setMessage("Table not found: " + order.getTableNo());
                response.setSuccess(false);
                return response;
            }

            if (null == order.getId()) {
                String seqId = "";
                sequence = sequenceService.findSequenceByName("Order");
                if (sequence == null) {
                    response.setCode(501);
                    response.setMessage("Order sequence not found. Please contact administrator.");
                    response.setSuccess(false);
                    return response;
                }
                seqId = (sequence.getPrefix() + String.valueOf((sequence.getCounter() + 1)));
                order.setOrderNo(seqId);
                order.setActive(true); // Set new orders as active by default

                // Increment the sequence counter immediately
                sequence.setCounter(sequence.getCounter() + 1);
                sequenceService.save(sequence);

                if (table.getTableNo().equals("Not for Table")) {
                    table.setAvailable(true);
                    tableService.save(table);

                    si = orderRepository.save(order);
                    response.setMessage("Order Created Successfully");
                } else if (table.isAvailable() && order.getSalesInvoiceRecords().size() > 0) {
                    table.setAvailable(false);
                    tableService.save(table);

                    si = orderRepository.save(order);
                    response.setMessage("Order Created Successfully");
                } else {
                    throw new Exception("Table Not available");
                }
            } else {
                si = order;
                Order avlOrd = orderRepository.findByOrderNo(order.getOrderNo());
                // set is printed true for old records
                for (SalesInvoiceRecord record : avlOrd.getSalesInvoiceRecords()) {
                    order.getSalesInvoiceRecords().forEach(e -> {
                        if (e.getId().equals(record.getId())) {
                            e.setPrinted(true);
                        }
                    });
                }
                orderRepository.save(order);
                response.setMessage("Order Updated Successfully");
            }
            response.setData(si.getId());
            response.setCode(200);
            return response;
        } catch (Exception ex) {
            LOGGER.error("Creating Order Failed on line " + ex.getStackTrace()[0].getLineNumber() + " : " +
                    ex.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            ex.printStackTrace();
            response.setCode(501);
            response.setMessage("Creating Order Failed");
            response.setData(ex.getMessage());
            return response;
        }
    }

    @Override
    public List<Order> findTop15ByOrderByIdDesc() {
        try {
            return orderRepository.findTop15ByOrderByIdDesc();
        } catch (Exception ex) {
            LOGGER.error("Find All Order Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Order> findAll() {
        try {
            return orderRepositoryTemplate.findAll();
        } catch (Exception ex) {
            LOGGER.error("Find All Order Failed " + ex.getMessage());
            return null;
        }
    }
    
    @Override
    public List<Order> findActive() {
        try {
            return orderRepository.findByActive(true);
        } catch (Exception ex) {
            LOGGER.error("Find Active Order Failed " + ex.getMessage());
            return null;
        }
    }
    
    

    @Override
    public List<Order> findByTableNo(String tableNo) {
        try {
            return orderRepository.findAllByTableNo(tableNo);
        } catch (Exception ex) {
            LOGGER.error("Find Order by Location Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Order findByOrderNo(String s) {
        try {
            return orderRepository.findByOrderNo(s);
        } catch (Exception ex) {
            LOGGER.error("Find All Order Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Order findById(String id) {
        try {
            return orderRepository.findById(id).get();
        } catch (Exception ex) {
            LOGGER.error("Find All Order Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Order> findByOrderNoLike(String s) {
        try {
            return orderRepository.findAllByOrderNoLike(s);
        } catch (Exception ex) {
            LOGGER.error("Find All Order Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Order> findByDate(LocalDate date) {
        try {
            return orderRepository.findByDateBetween(date, date.plusDays(1));
        } catch (Exception e) {
            LOGGER.error("Find All Order Failed " + e.getMessage());
            return null;
        }
    }

    @Override
    public boolean deleteRecord(Order order) {
        try {
            orderRepository.delete(order);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Order Delete Failed " + ex.getMessage());
            return false;
        }
    }

}