#!/usr/bin/env node

/**
 * Global Styling Application Script
 * 
 * This script applies the professional styling framework across the entire application
 * to ensure consistency in design and user experience.
 * 
 * Usage:
 * node apply-global-styling.js [module-name]
 * 
 * Examples:
 * node apply-global-styling.js all          # Apply to all modules
 * node apply-global-styling.js inventory    # Apply to inventory module only
 * node apply-global-styling.js trade        # Apply to trade module only
 */

const fs = require('fs');
const path = require('path');

// Configuration for different component types
const componentTypes = {
  management: {
    pattern: /manage|create|edit|form/i,
    template: 'management-component-template.html',
    priority: 1
  },
  list: {
    pattern: /list|table|grid/i,
    template: 'list-component-template.html',
    priority: 2
  },
  report: {
    pattern: /report|summary|analytics/i,
    template: 'report-component-template.html',
    priority: 3
  },
  dashboard: {
    pattern: /dashboard|overview|home/i,
    template: 'dashboard-component-template.html',
    priority: 4
  }
};

// Module paths
const modulePaths = {
  inventory: 'src/app/home/<USER>/inventory/components',
  trade: 'src/app/home/<USER>/trade/component',
  restaurant: 'src/app/home/<USER>/restaurant/component',
  admin: 'src/app/home/<USER>/component',
  hr: 'src/app/home/<USER>/hr/components',
  report: 'src/app/home/<USER>/report/components',
  reservation: 'src/app/home/<USER>/reservation/component',
  dashboard: 'src/app/home/<USER>/dashboard/components'
};

// CSS class mappings for standardization
const cssClassMappings = {
  // Card classes
  'card': 'card professional-card animate-fade-in',
  'card-header': 'card-header professional-card-header',
  'card-body': 'card-body professional-card-body',
  'card-footer': 'card-footer professional-card-footer',
  'card-title': 'card-title professional-card-title',
  
  // Button classes
  'btn btn-primary': 'btn btn-professional btn-professional-primary',
  'btn btn-secondary': 'btn btn-professional btn-professional-secondary',
  'btn btn-success': 'btn btn-professional btn-professional-success',
  'btn btn-danger': 'btn btn-professional btn-professional-danger',
  'btn btn-warning': 'btn btn-professional btn-professional-warning',
  'btn btn-outline-primary': 'btn btn-professional btn-professional-outline btn-professional-outline-primary',
  'btn btn-outline-secondary': 'btn btn-professional btn-professional-outline btn-professional-outline-secondary',
  
  // Form classes
  'form-control': 'form-control',
  'form-label': 'form-label',
  'form-group': 'form-group form-group-professional',
  
  // Table classes
  'table': 'table table-professional',
  'table-striped': 'table table-professional',
  'table-hover': 'table table-professional',
  
  // Badge classes
  'badge badge-success': 'badge badge-professional badge-professional-success',
  'badge badge-danger': 'badge badge-professional badge-professional-danger',
  'badge badge-warning': 'badge badge-professional badge-professional-warning',
  'badge badge-info': 'badge badge-professional badge-professional-info',
  'badge badge-secondary': 'badge badge-professional badge-professional-secondary',
  'badge badge-primary': 'badge badge-professional badge-professional-primary',
  
  // Search classes
  'input-group': 'input-group search-professional',
  
  // Pagination classes
  'pagination': 'pagination pagination-professional'
};

/**
 * Main function to apply global styling
 */
function applyGlobalStyling(targetModule = 'all') {
  console.log(`🎨 Starting global styling application for: ${targetModule}`);
  
  const modules = targetModule === 'all' ? Object.keys(modulePaths) : [targetModule];
  
  modules.forEach(module => {
    if (!modulePaths[module]) {
      console.warn(`⚠️  Module '${module}' not found. Skipping...`);
      return;
    }
    
    console.log(`\n📁 Processing ${module} module...`);
    processModule(module, modulePaths[module]);
  });
  
  console.log('\n✅ Global styling application completed!');
  generateStylingReport(modules);
}

/**
 * Process a specific module
 */
function processModule(moduleName, modulePath) {
  try {
    if (!fs.existsSync(modulePath)) {
      console.warn(`⚠️  Path not found: ${modulePath}`);
      return;
    }
    
    const components = findComponents(modulePath);
    console.log(`Found ${components.length} components in ${moduleName}`);
    
    components.forEach(component => {
      processComponent(component, moduleName);
    });
    
  } catch (error) {
    console.error(`❌ Error processing module ${moduleName}:`, error.message);
  }
}

/**
 * Find all components in a module
 */
function findComponents(modulePath) {
  const components = [];
  
  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const itemPath = path.join(dir, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        // Check if this directory contains a component
        const htmlFile = path.join(itemPath, `${item}.component.html`);
        const tsFile = path.join(itemPath, `${item}.component.ts`);
        const cssFile = path.join(itemPath, `${item}.component.css`);
        
        if (fs.existsSync(htmlFile) && fs.existsSync(tsFile)) {
          components.push({
            name: item,
            path: itemPath,
            htmlFile,
            tsFile,
            cssFile: fs.existsSync(cssFile) ? cssFile : null
          });
        } else {
          // Recursively scan subdirectories
          scanDirectory(itemPath);
        }
      }
    });
  }
  
  scanDirectory(modulePath);
  return components;
}

/**
 * Process a single component
 */
function processComponent(component, moduleName) {
  console.log(`  🔧 Processing: ${component.name}`);
  
  try {
    // Process HTML file
    if (fs.existsSync(component.htmlFile)) {
      processHtmlFile(component.htmlFile);
    }
    
    // Process CSS file
    if (component.cssFile && fs.existsSync(component.cssFile)) {
      processCssFile(component.cssFile);
    } else {
      // Create CSS file if it doesn't exist
      createCssFile(component.cssFile || path.join(component.path, `${component.name}.component.css`));
    }
    
    console.log(`    ✅ ${component.name} updated`);
    
  } catch (error) {
    console.error(`    ❌ Error processing ${component.name}:`, error.message);
  }
}

/**
 * Process HTML file to apply professional classes
 */
function processHtmlFile(htmlFile) {
  let content = fs.readFileSync(htmlFile, 'utf8');
  
  // Create backup
  fs.copyFileSync(htmlFile, htmlFile + '.backup');
  
  // Apply class mappings
  Object.entries(cssClassMappings).forEach(([oldClass, newClass]) => {
    const regex = new RegExp(`class="${oldClass}"`, 'g');
    content = content.replace(regex, `class="${newClass}"`);
    
    const regexSingle = new RegExp(`class='${oldClass}'`, 'g');
    content = content.replace(regexSingle, `class='${newClass}'`);
  });
  
  // Add professional container if not present
  if (!content.includes('container-fluid mt-3')) {
    content = content.replace(
      /<div class="container-fluid"([^>]*)>/g,
      '<div class="container-fluid mt-3"$1>'
    );
  }
  
  // Add button spacing classes
  content = addButtonSpacing(content);
  
  // Add professional card structure
  content = addProfessionalCardStructure(content);
  
  fs.writeFileSync(htmlFile, content);
}

/**
 * Add button spacing to HTML content
 */
function addButtonSpacing(content) {
  // Add btn-spacing class to buttons that don't have it
  content = content.replace(
    /<button([^>]*class="[^"]*btn[^"]*")([^>]*?)>/g,
    (match, classAttr, rest) => {
      if (!classAttr.includes('btn-spacing') && !match.includes('btn-spacing')) {
        return match.replace('>', ' class="btn-spacing">').replace('class="btn-spacing" class=', 'class="btn-spacing ');
      }
      return match;
    }
  );
  
  return content;
}

/**
 * Add professional card structure to content
 */
function addProfessionalCardStructure(content) {
  // This is a simplified version - in practice, you'd want more sophisticated parsing
  return content;
}

/**
 * Process CSS file to add professional styling
 */
function processCssFile(cssFile) {
  let content = fs.readFileSync(cssFile, 'utf8');
  
  // Create backup
  fs.copyFileSync(cssFile, cssFile + '.backup');
  
  // Add import for global professional theme
  const importStatement = `@import '../../../shared/styles/global-professional-theme.css';\n\n`;
  
  if (!content.includes('global-professional-theme.css')) {
    content = importStatement + content;
  }
  
  fs.writeFileSync(cssFile, content);
}

/**
 * Create CSS file with professional styling
 */
function createCssFile(cssFile) {
  const content = `@import '../../../shared/styles/global-professional-theme.css';

/* Component-specific styles */
/* Add any custom styles below this line */
`;
  
  fs.writeFileSync(cssFile, content);
}

/**
 * Generate styling report
 */
function generateStylingReport(modules) {
  console.log('\n📊 Styling Application Report');
  console.log('================================');
  console.log(`Modules processed: ${modules.join(', ')}`);
  console.log('\n🎯 Applied Features:');
  console.log('  ✅ Professional card layouts');
  console.log('  ✅ Consistent button styling with spacing');
  console.log('  ✅ Modern form controls');
  console.log('  ✅ Status badges with gradients');
  console.log('  ✅ Professional table styling');
  console.log('  ✅ Responsive design patterns');
  console.log('  ✅ Smooth animations and transitions');
  console.log('\n📝 Next Steps:');
  console.log('  1. Review updated components');
  console.log('  2. Test functionality');
  console.log('  3. Customize component-specific styles');
  console.log('  4. Update any hardcoded styling');
}

// Main execution
if (require.main === module) {
  const args = process.argv.slice(2);
  const targetModule = args[0] || 'all';
  
  applyGlobalStyling(targetModule);
}

module.exports = {
  applyGlobalStyling,
  processModule,
  processComponent,
  cssClassMappings
};
