{"scripts": {"standardize": "node src/app/shared/scripts/standardize-management-component.js", "standardize:help": "echo 'Usage: npm run standardize <component-path> <entity-name>'", "standardize:example": "npm run standardize src/app/inventory/components/model Model"}, "description": "Scripts for standardizing management components", "examples": {"inventory": {"model": "npm run standardize src/app/inventory/components/model Model", "category": "npm run standardize src/app/inventory/components/category Category"}, "trade": {"vendor": "npm run standardize src/app/trade/components/vendor Vendor", "customer": "npm run standardize src/app/trade/components/customer Customer"}, "restaurant": {"menu": "npm run standardize src/app/restaurant/components/menu Menu", "order": "npm run standardize src/app/restaurant/components/order Order"}}}