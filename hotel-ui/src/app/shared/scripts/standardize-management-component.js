#!/usr/bin/env node

/**
 * Management Component Standardization Script
 * 
 * This script automates the process of standardizing management components
 * to follow the professional design pattern established in the application.
 * 
 * Usage:
 * node standardize-management-component.js <component-path> <entity-name>
 * 
 * Example:
 * node standardize-management-component.js "src/app/inventory/components/model" "Model"
 */

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  templates: {
    html: path.join(__dirname, '../templates/management-component-template.html'),
    css: path.join(__dirname, '../templates/management-component-template.css'),
    ts: path.join(__dirname, '../templates/management-component-template.ts')
  },
  placeholders: {
    '{{ENTITY_NAME}}': '',
    '{{entity}}': '',
    '{{entities}}': '',
    '{{searchKey}}': '',
    '{{EntityService}}': '',
    '{{entityService}}': ''
  }
};

/**
 * Main function to standardize a management component
 */
function standardizeComponent(componentPath, entityName) {
  console.log(`🚀 Starting standardization of ${entityName} component...`);
  
  // Validate inputs
  if (!componentPath || !entityName) {
    console.error('❌ Error: Please provide component path and entity name');
    console.log('Usage: node standardize-management-component.js <component-path> <entity-name>');
    process.exit(1);
  }

  // Generate placeholders
  const placeholders = generatePlaceholders(entityName);
  
  // Process each file type
  try {
    processHtmlFile(componentPath, placeholders);
    processCssFile(componentPath, placeholders);
    processTypeScriptFile(componentPath, placeholders);
    
    console.log('✅ Component standardization completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Review the generated files');
    console.log('2. Customize form fields for entity-specific properties');
    console.log('3. Update imports and service references');
    console.log('4. Test the component functionality');
    console.log('5. Update any component-specific methods');
    
  } catch (error) {
    console.error('❌ Error during standardization:', error.message);
    process.exit(1);
  }
}

/**
 * Generate placeholder values based on entity name
 */
function generatePlaceholders(entityName) {
  const entity = entityName.toLowerCase();
  const entities = entity + 's';
  
  return {
    '{{ENTITY_NAME}}': entityName,
    '{{entity}}': entity,
    '{{entities}}': entities,
    '{{searchKey}}': 'key' + entityName,
    '{{EntityService}}': entityName + 'Service',
    '{{entityService}}': entity + 'Service'
  };
}

/**
 * Process HTML template file
 */
function processHtmlFile(componentPath, placeholders) {
  console.log('📄 Processing HTML file...');
  
  const htmlPath = path.join(componentPath, `${path.basename(componentPath)}.component.html`);
  const templatePath = config.templates.html;
  
  if (!fs.existsSync(templatePath)) {
    throw new Error(`Template file not found: ${templatePath}`);
  }
  
  let content = fs.readFileSync(templatePath, 'utf8');
  
  // Replace placeholders
  Object.entries(placeholders).forEach(([placeholder, value]) => {
    content = content.replace(new RegExp(placeholder, 'g'), value);
  });
  
  // Create backup of original file
  if (fs.existsSync(htmlPath)) {
    fs.copyFileSync(htmlPath, htmlPath + '.backup');
    console.log(`📦 Backup created: ${htmlPath}.backup`);
  }
  
  // Write new content
  fs.writeFileSync(htmlPath, content);
  console.log(`✅ HTML file updated: ${htmlPath}`);
}

/**
 * Process CSS template file
 */
function processCssFile(componentPath, placeholders) {
  console.log('🎨 Processing CSS file...');
  
  const cssPath = path.join(componentPath, `${path.basename(componentPath)}.component.css`);
  const templatePath = config.templates.css;
  
  if (!fs.existsSync(templatePath)) {
    throw new Error(`Template file not found: ${templatePath}`);
  }
  
  let content = fs.readFileSync(templatePath, 'utf8');
  
  // Create backup of original file
  if (fs.existsSync(cssPath)) {
    fs.copyFileSync(cssPath, cssPath + '.backup');
    console.log(`📦 Backup created: ${cssPath}.backup`);
  }
  
  // Write new content
  fs.writeFileSync(cssPath, content);
  console.log(`✅ CSS file updated: ${cssPath}`);
}

/**
 * Process TypeScript template file
 */
function processTypeScriptFile(componentPath, placeholders) {
  console.log('📝 Processing TypeScript file...');
  
  const tsPath = path.join(componentPath, `${path.basename(componentPath)}.component.ts`);
  const templatePath = config.templates.ts;
  
  if (!fs.existsSync(templatePath)) {
    throw new Error(`Template file not found: ${templatePath}`);
  }
  
  let content = fs.readFileSync(templatePath, 'utf8');
  
  // Replace placeholders
  Object.entries(placeholders).forEach(([placeholder, value]) => {
    content = content.replace(new RegExp(placeholder, 'g'), value);
  });
  
  // Create backup of original file
  if (fs.existsSync(tsPath)) {
    fs.copyFileSync(tsPath, tsPath + '.backup');
    console.log(`📦 Backup created: ${tsPath}.backup`);
  }
  
  // Write new content
  fs.writeFileSync(tsPath, content);
  console.log(`✅ TypeScript file updated: ${tsPath}`);
}

/**
 * Validate component structure
 */
function validateComponent(componentPath) {
  const requiredFiles = [
    `${path.basename(componentPath)}.component.html`,
    `${path.basename(componentPath)}.component.css`,
    `${path.basename(componentPath)}.component.ts`
  ];
  
  const missingFiles = requiredFiles.filter(file => 
    !fs.existsSync(path.join(componentPath, file))
  );
  
  if (missingFiles.length > 0) {
    throw new Error(`Missing component files: ${missingFiles.join(', ')}`);
  }
}

/**
 * Generate component analysis report
 */
function generateReport(componentPath, entityName) {
  console.log('\n📊 Component Analysis Report');
  console.log('================================');
  console.log(`Entity Name: ${entityName}`);
  console.log(`Component Path: ${componentPath}`);
  console.log(`Generated Files:`);
  console.log(`  - HTML: ${path.basename(componentPath)}.component.html`);
  console.log(`  - CSS: ${path.basename(componentPath)}.component.css`);
  console.log(`  - TypeScript: ${path.basename(componentPath)}.component.ts`);
  console.log('\n🎯 Standardization Features Applied:');
  console.log('  ✅ Professional card-based layout');
  console.log('  ✅ Consistent button spacing (ml-1 pattern)');
  console.log('  ✅ Modern search functionality');
  console.log('  ✅ Status badges with gradients');
  console.log('  ✅ Responsive design');
  console.log('  ✅ Smooth animations and hover effects');
  console.log('  ✅ Professional form styling');
  console.log('  ✅ Consistent pagination');
}

// Main execution
if (require.main === module) {
  const args = process.argv.slice(2);
  const componentPath = args[0];
  const entityName = args[1];
  
  standardizeComponent(componentPath, entityName);
}

module.exports = {
  standardizeComponent,
  generatePlaceholders,
  processHtmlFile,
  processCssFile,
  processTypeScriptFile
};
