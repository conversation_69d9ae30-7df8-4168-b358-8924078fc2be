/*
  GLOBAL PROFESSIONAL THEME
  
  This CSS file contains the standardized professional styling that should be
  applied across the entire application for consistency.
  
  USAGE:
  1. Import this file in styles.css or angular.json
  2. Apply classes consistently across all components
  3. Follow the established design patterns
  
  FEATURES:
  - Professional card layouts
  - Consistent button styling with proper spacing
  - Modern form controls
  - Status badges with gradients
  - Responsive design patterns
  - Smooth animations and transitions
*/

/* ===== GLOBAL VARIABLES ===== */
:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
  --success-gradient: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  --danger-gradient: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
  --warning-gradient: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
  --info-gradient: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
  
  --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --card-shadow-hover: 0 8px 25px rgba(0, 0, 0, 0.15);
  --border-radius: 12px;
  --border-radius-sm: 8px;
  --transition: all 0.3s ease;
  --transition-fast: all 0.2s ease;
  
  --text-primary: #495057;
  --text-secondary: #6c757d;
  --text-muted: #adb5bd;
  --bg-light: #f8f9fa;
  --bg-lighter: #e9ecef;
  --border-color: #dee2e6;
}

/* ===== PROFESSIONAL CARD STYLING ===== */
.professional-card {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  overflow: hidden;
  transition: var(--transition);
  background: white;
}

.professional-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--card-shadow-hover);
}

.professional-card-header {
  padding: 1.5rem;
  background: linear-gradient(135deg, var(--bg-light) 0%, var(--bg-lighter) 100%);
  border-bottom: 1px solid var(--border-color);
}

.professional-card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0;
}

.professional-card-body {
  padding: 1.5rem;
}

.professional-card-footer {
  padding: 1rem 1.5rem;
  background: white;
  border-top: 1px solid var(--border-color);
}

/* ===== PROFESSIONAL BUTTON STYLING ===== */
.btn-professional {
  border-radius: var(--border-radius-sm);
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  transition: var(--transition-fast);
  border: none;
  position: relative;
  overflow: hidden;
}

.btn-professional:hover {
  transform: translateY(-1px);
}

.btn-professional-lg {
  padding: 1rem 2rem;
  font-size: 1.1rem;
}

.btn-professional-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

/* Primary Button */
.btn-professional-primary {
  background: var(--primary-gradient);
  color: white;
}

.btn-professional-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  color: white;
}

/* Secondary Button */
.btn-professional-secondary {
  background: var(--secondary-gradient);
  color: white;
}

.btn-professional-secondary:hover {
  background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
  color: white;
}

/* Success Button */
.btn-professional-success {
  background: var(--success-gradient);
  color: white;
}

.btn-professional-success:hover {
  background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
  color: white;
}

/* Danger Button */
.btn-professional-danger {
  background: var(--danger-gradient);
  color: white;
}

.btn-professional-danger:hover {
  background: linear-gradient(135deg, #c82333 0%, #e0a800 100%);
  color: white;
}

/* Outline Buttons */
.btn-professional-outline {
  background: transparent;
  border: 2px solid;
}

.btn-professional-outline-primary {
  border-color: #667eea;
  color: #667eea;
}

.btn-professional-outline-primary:hover {
  background: #667eea;
  color: white;
}

.btn-professional-outline-secondary {
  border-color: var(--text-secondary);
  color: var(--text-secondary);
}

.btn-professional-outline-secondary:hover {
  background: var(--text-secondary);
  color: white;
}

/* Button Spacing */
.btn-spacing {
  margin-left: 0.5rem;
}

.btn-spacing:first-child {
  margin-left: 0;
}

/* ===== PROFESSIONAL FORM STYLING ===== */
.form-professional {
  margin-bottom: 0;
}

.form-professional .form-label {
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.form-professional .form-control {
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-color);
  padding: 0.75rem 1rem;
  transition: var(--transition-fast);
  font-size: 1rem;
}

.form-professional .form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  outline: none;
}

.form-professional .form-control-lg {
  padding: 1rem 1.25rem;
  font-size: 1.1rem;
}

.form-professional .form-control-sm {
  padding: 0.5rem 0.75rem;
  font-size: 0.9rem;
}

/* Professional Form Groups */
.form-group-professional {
  margin-bottom: 1.5rem;
}

.form-group-professional:last-child {
  margin-bottom: 0;
}

/* Professional Switches */
.form-switch-professional {
  background: var(--bg-light);
  padding: 1.5rem;
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-color);
  margin-bottom: 1rem;
}

.form-switch-professional .form-check-input:checked {
  background-color: #667eea;
  border-color: #667eea;
}

.form-switch-professional .form-check-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
}

.form-switch-professional .form-check-label {
  color: var(--text-primary);
  font-weight: 600;
}

.form-switch-professional .form-text {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* ===== PROFESSIONAL TABLE STYLING ===== */
.table-professional {
  margin-bottom: 0;
  background: white;
}

.table-professional thead th {
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 2px solid var(--border-color);
  padding: 1rem;
  background: var(--bg-light);
}

.table-professional tbody tr {
  cursor: pointer;
  transition: var(--transition-fast);
}

.table-professional tbody tr:hover {
  background-color: var(--bg-light);
  transform: scale(1.01);
}

.table-professional tbody tr.table-active {
  background-color: #e3f2fd;
  border-left: 4px solid #2196f3;
}

.table-professional .entity-name {
  font-weight: 500;
  color: var(--text-primary);
}

/* ===== PROFESSIONAL BADGE STYLING ===== */
.badge-professional {
  padding: 0.5rem 0.75rem;
  border-radius: 20px;
  font-weight: 500;
  font-size: 0.8rem;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.badge-professional-success {
  background: var(--success-gradient);
  color: white;
}

.badge-professional-danger {
  background: var(--danger-gradient);
  color: white;
}

.badge-professional-warning {
  background: var(--warning-gradient);
  color: white;
}

.badge-professional-info {
  background: var(--info-gradient);
  color: white;
}

.badge-professional-secondary {
  background: var(--secondary-gradient);
  color: white;
}

.badge-professional-primary {
  background: var(--primary-gradient);
  color: white;
}

/* ===== PROFESSIONAL SEARCH STYLING ===== */
.search-professional {
  width: 300px;
}

.search-professional .input-group-text {
  border: 1px solid var(--border-color);
  background: var(--bg-light);
}

.search-professional .form-control {
  border-left: none;
}

.search-professional .form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* ===== PROFESSIONAL PAGINATION ===== */
.pagination-professional {
  margin-bottom: 0;
}

.pagination-professional .page-link {
  border-radius: 6px;
  margin: 0 2px;
  border: 1px solid var(--border-color);
  color: #667eea;
  padding: 0.5rem 0.75rem;
  transition: var(--transition-fast);
}

.pagination-professional .page-link:hover {
  background-color: #667eea;
  border-color: #667eea;
  color: white;
  transform: translateY(-1px);
}

.pagination-professional .page-item.active .page-link {
  background-color: #667eea;
  border-color: #667eea;
  color: white;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .search-professional {
    width: 100%;
    margin-top: 1rem;
  }
  
  .professional-card-header {
    padding: 1rem;
  }
  
  .professional-card-body {
    padding: 1rem;
  }
  
  .btn-professional {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
  
  .btn-professional-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeInUp 0.5s ease-out;
}

/* ===== UTILITY CLASSES ===== */
.text-professional-primary {
  color: var(--text-primary);
}

.text-professional-secondary {
  color: var(--text-secondary);
}

.text-professional-muted {
  color: var(--text-muted);
}

.bg-professional-light {
  background-color: var(--bg-light);
}

.border-professional {
  border: 1px solid var(--border-color);
}

.shadow-professional {
  box-shadow: var(--card-shadow);
}

.shadow-professional-hover {
  box-shadow: var(--card-shadow-hover);
}

/* ===== EMPTY STATE STYLING ===== */
.empty-state-professional {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-muted);
}

.empty-state-professional i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state-professional h5 {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.empty-state-professional p {
  color: var(--text-muted);
  margin-bottom: 0;
}
