/*
  STANDARDIZED MANAGEMENT COMPONENT TYPESCRIPT TEMPLATE
  
  This TypeScript template should be used for all management components that follow the list + form pattern.
  
  USAGE:
  1. Copy this template to your component TypeScript file
  2. Replace placeholders with your specific entity data:
     - {{Entity}} - Replace with your entity class name (e.g., "Brand", "Supplier", "Category")
     - {{entity}} - Replace with your entity variable name (e.g., "brand", "supplier", "category")
     - {{entities}} - Replace with your entities array name (e.g., "brands", "suppliers", "categories")
     - {{EntityService}} - Replace with your service class name (e.g., "BrandService", "SupplierService")
     - {{entityService}} - Replace with your service variable name (e.g., "brandService", "supplierService")
  3. Import your specific entity class and service
  4. Add any component-specific methods and properties
  
  FEATURES INCLUDED:
  - Standard CRUD operations
  - Search functionality
  - Pagination
  - Modal support
  - Error handling
  - Consistent method naming
*/

import { Component, OnInit } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
// Import your specific entity and service here
// import { {{Entity}} } from '../model/{{entity}}';
// import { {{EntityService}} } from '../service/{{entity}}.service';
import { NotificationService } from '../../../core/service/notification.service';

@Component({
  selector: 'app-{{entity}}',
  templateUrl: './{{entity}}.component.html',
  styleUrls: ['./{{entity}}.component.css']
})
export class {{Entity}}Component implements OnInit {
  
  // Modal support
  modalRef: BsModalRef;
  isModal: boolean = false;

  // Entity management
  {{entity}} = new {{Entity}}();
  {{entities}}: Array<{{Entity}}> = [];
  
  // Search and selection
  key{{Entity}}: string = '';
  selectedRow: number = -1;
  
  // Pagination
  page: number = 1;
  pageSize: number = 10;
  collectionSize: number = 0;

  constructor(
    private {{entityService}}: {{EntityService}},
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    this.init{{Entity}}();
    this.findAll();
    
    // Check if component is opened as modal
    this.isModal = !!this.modalRef;
  }

  /**
   * Initialize entity with default values
   */
  init{{Entity}}(): void {
    this.{{entity}} = new {{Entity}}();
    this.{{entity}}.active = true;
    this.selectedRow = -1;
  }

  /**
   * Find all entities with pagination
   */
  findAll(): void {
    this.{{entityService}}.findAll(this.page - 1, this.pageSize).subscribe(
      (result: any) => {
        this.{{entities}} = result.content || [];
        this.collectionSize = result.totalElements || 0;
      },
      (error) => {
        this.notificationService.showError('Failed to load {{entities}}');
        console.error('Find all {{entities}} error:', error);
      }
    );
  }

  /**
   * Search entities by name
   */
  load{{Entity}}s(): void {
    if (this.key{{Entity}} && this.key{{Entity}}.trim().length > 0) {
      this.{{entityService}}.findByNameLike(this.key{{Entity}}).subscribe(
        (data: Array<{{Entity}}>) => {
          this.{{entities}} = data || [];
        },
        (error) => {
          this.notificationService.showError('Failed to search {{entities}}');
          console.error('Search {{entities}} error:', error);
        }
      );
    } else {
      this.findAll();
    }
  }

  /**
   * Set selected entity from typeahead
   */
  setSelected{{Entity}}(event: any): void {
    if (event && event.item) {
      this.{{entity}}Detail(event.item, -1);
    }
  }

  /**
   * Handle entity selection from table
   */
  {{entity}}Detail({{entity}}: {{Entity}}, index: number): void {
    this.{{entity}} = { ...{{entity}} }; // Create a copy to avoid direct mutation
    this.selectedRow = index;
  }

  /**
   * Save entity (create or update)
   */
  save{{Entity}}(): void {
    this.{{entityService}}.save(this.{{entity}}).subscribe(
      (result: any) => {
        if (result.code === 200) {
          this.notificationService.showSuccess(result.message || '{{Entity}} saved successfully');
          this.findAll();
          this.clear();
        } else {
          this.notificationService.showError(result.message || 'Failed to save {{entity}}');
        }
      },
      (error) => {
        this.notificationService.showError('An error occurred while saving the {{entity}}');
        console.error('Save {{entity}} error:', error);
      }
    );
  }

  /**
   * Clear form and reset selection
   */
  clear(): void {
    this.init{{Entity}}();
    this.key{{Entity}} = '';
  }

  /**
   * Handle pagination change
   */
  pageChanged(event: any): void {
    this.page = event.page;
    this.findAll();
  }

  /**
   * Close modal (for modal mode)
   */
  closeModal(): void {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

  /**
   * Set modal reference (for modal mode)
   */
  setModalRef(ref: BsModalRef): void {
    this.modalRef = ref;
    this.isModal = true;
  }

  // Add any component-specific methods below this line
}
