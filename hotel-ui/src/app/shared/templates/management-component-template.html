<!-- 
  STANDARDIZED MANAGEMENT COMPONENT TEMPLATE
  
  This template should be used for all management components that follow the list + form pattern.
  
  USAGE:
  1. Copy this template to your component HTML file
  2. Replace placeholders with your specific entity data:
     - {{ENTITY_NAME}} - Replace with your entity name (e.g., "Brand", "Supplier", "Category")
     - {{entity}} - Replace with your entity variable name (e.g., "brand", "supplier", "category")
     - {{entities}} - Replace with your entities array name (e.g., "brands", "suppliers", "categories")
     - {{searchKey}} - Replace with your search key variable (e.g., "keyBrand", "keySupplier")
     - Add your specific form fields in the form section
     - Add your specific table columns in the table section
  
  FEATURES INCLUDED:
  - Professional card-based layout
  - Responsive design
  - Search functionality
  - Pagination
  - Status indicators
  - Modern styling
  - Consistent button placement
-->

<div class="container-fluid mt-3" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <!-- <PERSON><PERSON> Close Button (only for modal mode) -->
  <div *ngIf="isModal" class="d-flex justify-content-end mb-3">
    <button type="button" class="btn-close" aria-label="Close" (click)="closeModal()"></button>
  </div>

  <div class="row g-4">
    <!-- {{ENTITY_NAME}} List Section -->
    <div class="col-lg-8">
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">{{ENTITY_NAME}}s</h5>
            <div class="search-container">
              <div class="input-group">
                <span class="input-group-text bg-light border-end-0">
                  <i class="fas fa-search text-muted"></i>
                </span>
                <input [(ngModel)]="{{searchKey}}"
                       [typeahead]="{{entities}}"
                       (typeaheadLoading)="load{{ENTITY_NAME}}s()"
                       (typeaheadOnSelect)="setSelected{{ENTITY_NAME}}($event)"
                       [typeaheadOptionsLimit]="7"
                       typeaheadWaitMs="1000"
                       typeaheadOptionField="name"
                       placeholder="Search {{entity}}s..."
                       autocomplete="off"
                       class="form-control border-start-0"
                       name="{{entity}}Search">
              </div>
            </div>
          </div>
        </div>
        
        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <!-- Add your specific columns here -->
                  <th class="border-0 fw-semibold">{{ENTITY_NAME}} Name</th>
                  <th class="border-0 fw-semibold text-center">Status</th>
                  <th class="border-0 fw-semibold text-center">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let {{entity}} of {{entities}}; let i = index"
                    class="table-row"
                    [class.table-active]="i === selectedRow"
                    (click)="{{entity}}Detail({{entity}}, i)">
                  <!-- Add your specific column data here -->
                  <td class="align-middle">
                    <span class="entity-name">{{ {{entity}}.name }}</span>
                  </td>
                  <td class="align-middle text-center">
                    <span class="badge" [class]="{{entity}}.active ? 'badge-success' : 'badge-danger'">
                      <i class="fas" [class]="{{entity}}.active ? 'fa-check-circle' : 'fa-times-circle'"></i>
                      {{ {{entity}}.active ? 'Active' : 'Inactive' }}
                    </span>
                  </td>
                  <td class="align-middle text-center">
                    <button class="btn btn-sm btn-outline-primary" 
                            (click)="{{entity}}Detail({{entity}}, i); $event.stopPropagation()">
                      <i class="fas fa-edit"></i>
                    </button>
                  </td>
                </tr>
                <tr *ngIf="{{entities}}?.length === 0">
                  <td colspan="3" class="text-center py-4 text-muted">
                    <i class="fas fa-inbox fa-2x mb-3 d-block"></i>
                    No {{entity}}s found
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        
        <div class="card-footer bg-white border-top" *ngIf="collectionSize > pageSize">
          <div class="d-flex justify-content-center">
            <pagination class="pagination-sm"
                        [totalItems]="collectionSize"
                        [(ngModel)]="page"
                        [boundaryLinks]="true"
                        [maxSize]="5"
                        (pageChanged)="pageChanged($event)">
            </pagination>
          </div>
        </div>
      </div>
    </div>

    <!-- {{ENTITY_NAME}} Form Section -->
    <div class="col-lg-4">
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <h5 class="card-title mb-0">
            {{ {{entity}}.id ? 'Edit {{ENTITY_NAME}}' : '{{ENTITY_NAME}} Details' }}
          </h5>
        </div>
        
        <div class="card-body">
          <form #manage{{ENTITY_NAME}}Form="ngForm" (ngSubmit)="save{{ENTITY_NAME}}()">
            
            <!-- Add your specific form fields here -->
            <div class="form-group mb-4">
              <label class="form-label fw-semibold">{{ENTITY_NAME}} Name</label>
              <input type="text" 
                     class="form-control form-control-lg" 
                     [(ngModel)]="{{entity}}.name" 
                     name="{{entity}}Name"
                     placeholder="Enter {{entity}} name"
                     required>
            </div>

            <!-- Status Toggle -->
            <div class="form-group mb-4">
              <label class="form-label fw-semibold mb-3">{{ENTITY_NAME}} Settings</label>
              
              <div class="status-toggles">
                <div class="form-check form-switch">
                  <input class="form-check-input" 
                         type="checkbox" 
                         id="activeSwitch" 
                         [(ngModel)]="{{entity}}.active"
                         name="active">
                  <label class="form-check-label fw-semibold" for="activeSwitch">
                    Active Status
                  </label>
                  <div class="form-text">Enable this {{entity}} for use</div>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="d-grid gap-2">
              <button type="submit" class="btn btn-primary btn-lg">
                {{ {{entity}}.id ? 'Update {{ENTITY_NAME}}' : 'Save {{ENTITY_NAME}}' }}
              </button>
              <button type="button" class="btn btn-outline-secondary" (click)="clear()">
                Clear
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
