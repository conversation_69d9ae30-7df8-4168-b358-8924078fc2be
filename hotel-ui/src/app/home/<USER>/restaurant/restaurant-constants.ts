import {environment} from '../../../../environments/environment';

export class ApiConstants {

  public static API_URL = environment.apiUrl;

  public static SAVE_BANK_ACCOUNT = ApiConstants.API_URL + 'bankAccount/save';
  public static GET_ALL_BANK_ACCOUNTS = ApiConstants.API_URL + 'bankAccount/findAllBankAccounts';
  public static SEARCH_BANK_ACCOUNT = ApiConstants.API_URL + 'bankAccount/findById';
  public static GET_PAGES = ApiConstants.API_URL + 'bankAccount/findAllPage';
  public static SEARCH_BANK_ACCOUNT_NO = ApiConstants.API_URL + 'bankAccount/search';

  public static SAVE_PURCHASE_INVOICE = ApiConstants.API_URL + 'purchaseInvoice/save';
  public static GET_PURCHASE_INVOICE_PAGINATION = ApiConstants.API_URL + 'purchaseInvoice/findAllPages';
  public static FIND_ALL_BY_INVOICE_LIKE = ApiConstants.API_URL + 'purchaseInvoice/searchByName';
  public static FIND_ALL_BY_SUPPLIER = ApiConstants.API_URL + 'purchaseInvoice/searchBySupplier';
  public static FIND_ALL_BY_PURCHASE_INVOICE_NO = ApiConstants.API_URL + 'purchaseInvoice/searchByInvoiceNo';
  public static FIND_ALL_BY_INVOICE_ID = ApiConstants.API_URL + 'purchaseInvoice/findById';

  public static SAVE_DEPOSIT = ApiConstants.API_URL + 'deposit/save';
  public static FIND_BY_CHEQUE = ApiConstants.API_URL + 'deposit/findByCheque';

  public static GET_ALL_PAYMENT_METHOD = ApiConstants.API_URL + 'paymentMethod/findAll';
  public static FIND_PAYMENT_METHOD_BY_ID = ApiConstants.API_URL + 'paymentMethod/findById';

  public static SAVE_QUOTATION = ApiConstants.API_URL + 'quotation/save';
  public static GET_QUOTATION = ApiConstants.API_URL + 'quotation/getAll';
  public static FIND_QUOTATION = ApiConstants.API_URL + 'quotation/findAll';
  public static FIND_ALL_BY_QUOTATION_ID = ApiConstants.API_URL + 'quotation/findAllQId';
  public static FIND_BY_QUOTATION_ID = ApiConstants.API_URL + 'quotation/findQuotationByQId';
  public static FIND_ALL_QUOTATION_BY_CUSTOMER = ApiConstants.API_URL + 'quotation/findAllByCustomer';

  public static SAVE_SALES_INVOICE = ApiConstants.API_URL + 'salesInvoice/save';
  public static GET_SALES_INVOICE = ApiConstants.API_URL + 'salesInvoice/findAllPages';
  public static GET_PENDING_SALES_INVOICE = ApiConstants.API_URL + 'salesInvoice/findAllPendingPages';
  public static FIND_SI_BY_CUST_NIC_BR = ApiConstants.API_URL + 'salesInvoice/findAllByCustomer';
  public static FIND_SI_BY_INV_NO = ApiConstants.API_URL + 'salesInvoice/findByInvoiceNo';
  public static FIND_SI_BY_INV_NO_LIKE = ApiConstants.API_URL + 'salesInvoice/findByInvoiceNoLike';
  public static FIND_SI_BY_JOB_NO = ApiConstants.API_URL + 'salesInvoice/findByJobNo';
  public static CANCEL_SALES_INVOICE = ApiConstants.API_URL + 'salesInvoice/cancel';
  public static PAY_SI_BALANCE = ApiConstants.API_URL + 'salesInvoice/payBalance';
  public static FIND_BY_DATE = ApiConstants.API_URL + 'salesInvoice/findByDate';
  public static GET_CALCULATED_AMOUNT_BY_COUNTER_AND_PAYMENT_METHOD
    = ApiConstants.API_URL + 'salesInvoice/calculateAmountByCounterAndPaymentMethod';
  public static GET_CALCULATED_TOTAL_CASHLESS_AMOUNT_WITHOUT_CREDIT
    = ApiConstants.API_URL + 'salesInvoice/calculateTotalCashlessAmountWithoutCredit';
  public static GET_CALCULATED_CREDIT_AMOUNT_BY_COUNTER =
    ApiConstants.API_URL + 'salesInvoice/calculateCreditAmountByCounter';
  public static GET_CALCULATED_TOTAL_SALES_AMOUNT = ApiConstants.API_URL + 'salesInvoice/calculateTotalSalesAmount';

  public static SAVE_CUSTOMER = ApiConstants.API_URL + 'customer/save';
  public static FIND_CUSTOMER_BY_ID = ApiConstants.API_URL + 'customer/findById';
  public static FIND_ALL_CUSTOMERS = ApiConstants.API_URL + 'customer/findAll';
  public static FIND_BY_CUSTOMER_NAME = ApiConstants.API_URL + 'customer/searchByName';
  public static FIND_CUSTOMER_BY_NIC_LIKE = ApiConstants.API_URL + 'customer/searchByNicLike';
  public static FIND_CUSTOMER_BY_TP_LIKE = ApiConstants.API_URL + 'customer/searchByTpLike';
  public static CUSTOMER_NIC_CHECK = ApiConstants.API_URL + 'customer/checkNic';

  public static SAVE_SUPPLIER = ApiConstants.API_URL + 'supplier/save';
  public static GET_SUPPLIERS = ApiConstants.API_URL + 'supplier/findAll';
  public static DELETE_SUPPLIER = ApiConstants.API_URL + 'supplier/delete';
  public static GET_LAST_SUPPLIER = ApiConstants.API_URL + 'supplier/getLastSupplier';
  public static SEARCH_SUPPLIER_BY_NAME_LIKE = ApiConstants.API_URL + 'supplier/searchByName';
  public static GET_SUPPLIER_BY_ID = ApiConstants.API_URL + 'supplier/searchById';

  public static SAVE_EXPENSE_TYPE = ApiConstants.API_URL + 'expenseType/save';
  public static GET_EXPENSE_TYPE = ApiConstants.API_URL + 'expenseType/findAllPage';
  public static SEARCH_EXPENSE_TYPE = ApiConstants.API_URL + 'expenseType/findByName';

  public static SAVE_EXPENSE = ApiConstants.API_URL + 'expense/save';

  public static FIND_CASHIER_BY_COUNTER = ApiConstants.API_URL + 'cashier/findByCounter'

  public static DAY_START = ApiConstants.API_URL + 'cashRecord/dayStart';
  public static ADD_CASH = ApiConstants.API_URL + 'cashRecord/addCash'
  public static WITHDRAW_CASH = ApiConstants.API_URL + 'cashRecord/withdrawCash';
  public static FIND_BY_COUNTER_AND_DATES_BETWEEN = ApiConstants.API_URL + 'cashRecord/findByCounterAndDates';
  public static FIND_BY_TYPE_AND_DATES_BETWEEN = ApiConstants.API_URL + 'cashRecord/findByTypeAndDates';
  public static FIND_BY_COUNTER_AND_TYPE_AND_DATES_BETWEEN = ApiConstants.API_URL + 'cashRecord/findByCounterAndTypeAndDates';
  public static GET_DAY_END_WITHDRAWAL_AMOUNT = ApiConstants.API_URL + 'cashRecord/getDayEndWithdrawalAmount';
  public static GET_CALCULATED_TOTAL_CASH_OUT = ApiConstants.API_URL + 'cashRecord/calculateTotalCashOut';
  public static GET_CALCULATED_TOTAL_CASH_IN = ApiConstants.API_URL + 'cashRecord/calculateTotalCashIn'
  public static FIND_BY_DATES_BETWEEN = ApiConstants.API_URL + 'cashRecord/findByDates';
  public static GET_CALCULATED_OTHER_CASH_IN = ApiConstants.API_URL + 'cashRecord/calculateOtherCashIn';
  public static GET_CALCULATED_CASH_OUT = ApiConstants.API_URL + 'cashRecord/calculateCashOut';


  public static SAVE_CASHIER_HISTORY = ApiConstants.API_URL + 'cashierHistory/dayClose'

  public static GET_ALL_INCOMPLETE = ApiConstants.API_URL + 'salesInvoice/findAllIncomplete';
  public static FIND_BY_PAYMENT_METHOD = ApiConstants.API_URL + 'salesInvoice/findAllByPaymentMethod';
  public static FIND_ALL_BY_PAYMENT_STATUS = ApiConstants.API_URL + 'salesInvoice/findAllByPaymentStatus';

  public static FIND_ALL_BY_NAME_LIKE = ApiConstants.API_URL + 'shot/findByNameLike';
  public static SAVE_SHOT = ApiConstants.API_URL + 'shot/save';
  public static GET_SHOTS = ApiConstants.API_URL + 'shot/findAll';

  public static GET_ORDERS = ApiConstants.API_URL + 'order/findAll';
  public static GET_ACTIVE_ORDERS = ApiConstants.API_URL + 'order/findActive';
  public static FIND_ALL_BY_ORDER_ID = ApiConstants.API_URL + 'order/findById';
  public static SAVE_ORDER = ApiConstants.API_URL + 'order/save';
  public static DELETE_ORDER = ApiConstants.API_URL + 'order/delete';

  public static SAVE_SHOW_TABLE = ApiConstants.API_URL + 'table/save';
  public static FIND_ALL_TABLE_PAGEABLE = ApiConstants.API_URL + 'table/findAllPageable';
  public static FIND_ALL_TABLES = ApiConstants.API_URL + 'table/findAll';
  public static FIND_ALL_TABLES_BY_TABLE_NO_LIKE = ApiConstants.API_URL + 'table/searchByTableNoLike';
}


