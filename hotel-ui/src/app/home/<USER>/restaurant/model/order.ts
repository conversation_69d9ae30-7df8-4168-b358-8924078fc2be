import { SalesInvoiceRecord } from "../../trade/model/sales-invoice-record";
import { Table } from "./table";
import { MetaData } from "../../../core/model/metaData";

export class Order {
  id: string;
  orderNo: string;
  salesType: MetaData;
  date: Date;
  salesInvoiceRecords: SalesInvoiceRecord[];
  subTotal: number;
  totalAmount: number;
  payment: number
  serviceCharge: number;
  tableNo: string;
  active: boolean;
}
