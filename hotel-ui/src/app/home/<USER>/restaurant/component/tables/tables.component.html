<div class="container-fluid">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="mb-0 text-primary fw-bold border-bottom pb-2">MANAGE TABLES</h2>
  </div>
  <div class="row">
    <div class="col-md-6">
      <div class="form-group">
        <input [(ngModel)]="keyTableNo"
               [typeahead]="tables"
               (typeaheadLoading)="loadTables()"
               (typeaheadOnSelect)="setSelectedTable()"
               [typeaheadOptionsLimit]="7"
               typeaheadWaitMs="1000"
               typeaheadOptionField="tableNo"
               placeholder="Search By Table No"
               autocomplete="off"
               size="16"
               required
               class="form-control form-control-lg" name="tableNo">
      </div>
      <table class="table table-striped">
        <thead>
        <tr>
          <th>Table No</th>
          <th>Note</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let tbl of tables,let i=index"
            (click)="tableDetail(tbl,i)"
            [class.active]="i === selectedRow">
          <td>{{tbl.tableNo}}</td>
        </tr>
        </tbody>
      </table>
      <div class="row">
        <div class="col-xs-12 col-12">
          <pagination class="pagination-sm justify-content-center"
                      [totalItems]="collectionSize"
                      [(ngModel)]="page"
                      [boundaryLinks]="true"
                      [maxSize]="10"
                      (pageChanged)="pageChanged($event)">
          </pagination>
        </div>
      </div>
    </div>

    <div class="col-md-6">
      <form #manageTableForm="ngForm" (ngSubmit)="saveTable(); manageTableForm.reset() ">
        <div class="form-group">
          <label class="form-label fw-bold">Table No </label>
          <input type="text" #tblNo="ngModel"
                 class="form-control form-control-lg" id="tblNo" [(ngModel)]="table.tableNo" name="bName"
                 placeholder="Auto-generated if left empty" readonly>
          <small class="form-text text-muted">Table number will be auto-generated if left empty</small>
        </div>

        <div class="form-check checkbox mr-2">
          <input class="form-check-input" id="check2" name="check2" type="checkbox" value=""
                 [(ngModel)]="isActive">
          <label class="form-check-label" for="check3">Active</label>
        </div>
        <div class="form-check checkbox mr-2">
          <input class="form-check-input" id="check3" name="check3" type="checkbox" value=""
                 [(ngModel)]="isAvailable">
          <label class="form-check-label" for="check3">Available</label>
        </div>

        <div class="text-right mt-3">
          <button type="submit" class="btn btn-primary btn-lg mr-2">Save</button>
          <button type="button" class="btn btn-secondary btn-lg" (click)="clear()">Clear</button>
        </div>
      </form>
    </div>
  </div>
</div>
