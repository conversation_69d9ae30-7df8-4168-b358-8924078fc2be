<div class="container-fluid mt-3">
  <div class="row g-4">
    <!-- Tables List Section -->
    <div class="col-lg-8">
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Tables</h5>
            <div class="search-container">
              <div class="input-group">
                <span class="input-group-text bg-light border-end-0">
                  <i class="fas fa-search text-muted"></i>
                </span>
                <input [(ngModel)]="keyTableNo"
                       [typeahead]="tables"
                       (typeaheadLoading)="loadTables()"
                       (typeaheadOnSelect)="setSelectedTable()"
                       [typeaheadOptionsLimit]="7"
                       typeaheadWaitMs="1000"
                       typeaheadOptionField="tableNo"
                       placeholder="Search tables..."
                       autocomplete="off"
                       class="form-control border-start-0"
                       name="tableNo">
              </div>
            </div>
          </div>
        </div>

        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th class="border-0 fw-semibold">
                    <i class="fas fa-hashtag me-2"></i>Table No
                  </th>
                  <th class="border-0 fw-semibold">
                    <i class="fas fa-sticky-note me-2"></i>Note
                  </th>
                  <th class="border-0 fw-semibold text-center">
                    <i class="fas fa-toggle-on me-2"></i>Status
                  </th>
                  <th class="border-0 fw-semibold text-center">
                    <i class="fas fa-chair me-2"></i>Availability
                  </th>
                  <th class="border-0 fw-semibold text-center">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let tbl of tables; let i = index"
                    class="table-row"
                    [class.table-active]="i === selectedRow"
                    (click)="tableDetail(tbl, i)">
                  <td class="align-middle">
                    <span class="table-number-badge">{{ tbl.tableNo }}</span>
                  </td>
                  <td class="align-middle">
                    <span class="text-muted">{{ tbl.note || 'No notes' }}</span>
                  </td>
                  <td class="align-middle text-center">
                    <span class="badge" [class]="tbl.active ? 'badge-success' : 'badge-danger'">
                      <i class="fas" [class]="tbl.active ? 'fa-check-circle' : 'fa-times-circle'"></i>
                      {{ tbl.active ? 'Active' : 'Inactive' }}
                    </span>
                  </td>
                  <td class="align-middle text-center">
                    <span class="badge" [class]="tbl.available ? 'badge-primary' : 'badge-warning'">
                      <i class="fas" [class]="tbl.available ? 'fa-chair' : 'fa-user-friends'"></i>
                      {{ tbl.available ? 'Available' : 'Occupied' }}
                    </span>
                  </td>
                  <td class="align-middle text-center">
                    <button class="btn btn-sm btn-outline-primary" (click)="tableDetail(tbl, i); $event.stopPropagation()">
                      <i class="fas fa-edit"></i>
                    </button>
                  </td>
                </tr>
                <tr *ngIf="tables?.length === 0">
                  <td colspan="5" class="text-center py-4 text-muted">
                    <i class="fas fa-table fa-2x mb-3 d-block"></i>
                    No tables found
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div class="card-footer bg-white border-top" *ngIf="collectionSize > pageSize">
          <div class="d-flex justify-content-center">
            <pagination class="pagination-sm"
                        [totalItems]="collectionSize"
                        [(ngModel)]="page"
                        [boundaryLinks]="true"
                        [maxSize]="5"
                        (pageChanged)="pageChanged($event)">
            </pagination>
          </div>
        </div>
      </div>
    </div>

    <!-- Table Form Section -->
    <div class="col-lg-4">
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <h5 class="card-title mb-0">
            {{ table.id ? 'Edit Table' : 'Table Details' }}
          </h5>
        </div>

        <div class="card-body">
          <form #manageTableForm="ngForm" (ngSubmit)="saveTable()">
            <!-- Table Number Field -->
            <div class="form-group mb-4">
              <label class="form-label fw-semibold">Table Number</label>
              <input type="text"
                     class="form-control form-control-lg"
                     [(ngModel)]="table.tableNo"
                     name="tableNo"
                     placeholder="Auto-generated"
                     readonly>
              <div class="form-text">Table number is automatically generated</div>
            </div>

            <!-- Note Field -->
            <div class="form-group mb-4">
              <label class="form-label fw-semibold">Notes</label>
              <textarea class="form-control"
                        rows="3"
                        [(ngModel)]="table.note"
                        name="note"
                        placeholder="Add any notes about this table..."></textarea>
            </div>

            <!-- Status Toggles -->
            <div class="form-group mb-4">
              <label class="form-label fw-semibold mb-3">Table Settings</label>

              <div class="status-toggles">
                <div class="form-check form-switch mb-3">
                  <input class="form-check-input"
                         type="checkbox"
                         id="activeSwitch"
                         [(ngModel)]="isActive"
                         name="active">
                  <label class="form-check-label fw-semibold" for="activeSwitch">
                    Active Status
                  </label>
                  <div class="form-text">Enable this table for use</div>
                </div>

                <div class="form-check form-switch">
                  <input class="form-check-input"
                         type="checkbox"
                         id="availableSwitch"
                         [(ngModel)]="isAvailable"
                         name="available">
                  <label class="form-check-label fw-semibold" for="availableSwitch">
                    Available for Seating
                  </label>
                  <div class="form-text">Mark table as available for customers</div>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="d-grid gap-2">
              <button type="submit" class="btn btn-primary btn-lg">
                {{ table.id ? 'Update Table' : 'Save Table' }}
              </button>
              <button type="button" class="btn btn-outline-secondary" (click)="clear()">
                Clear
              </button>
              <button type="button" class="btn btn-success btn-lg mt-3" (click)="clear()">
                Add New Table
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
