/* Import Professional Theme */
@import '../../../../../shared/styles/global-professional-theme.css';

body {
  height: 100vh;
}

.select-item:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 20px rgba(0, 0, 0, .12), 0 4px 8px rgba(0, 0, 0, .06);
}

.item-card {
  background-color: #EFF2F9;
  height: 10rem;
}

.item-card:hover {
  background-color: #e9ecef;
  transform: scale(1.05);
  box-shadow: 0 10px 20px rgba(0, 0, 0, .12), 0 4px 8px rgba(0, 0, 0, .06);
}

.theme-color-outline-button {
  color: rgb(89, 12, 48);
  background-color: #EFF2F9;
  border: 1px solid rgb(89, 12, 48);
}

/* Full-screen layout styles */
.container-fluid {
  height: 100vh;
  overflow: hidden;
}

.theme-color {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 10px 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-section {
  min-height: 45px;
}

.header-section h3 { /* Changed from h2 to h3 to match HTML */
  color: white;
  font-weight: 600;
}

.select-item {
  color: white;
  transition: all 0.3s ease;
}

.select-item:hover {
  color: #ffd700;
  transform: scale(1.1);
}

.content-section {
  height: calc(100vh - 80px);
  padding: 0;
}

.content-section .row {
  height: 100%;
  margin: 0;
}

.content-section .col-md-7,
.content-section .col-md-3 {
  height: 100%;
  padding: 15px;
}

/* Category and item cards */
.card {
  border: 2px solid #e9ecef;
  border-radius: 10px;
  transition: all 0.3s ease;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  cursor: pointer;
}

.card:hover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Table selection styling */
.table-selection {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border-radius: 8px;
  color: white;
  font-weight: 600;
}

.table-selection.bg-success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

.table-not-selected {
  background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
  border-radius: 8px;
  color: white;
  font-weight: 600;
}

/* Invoice items styling */
.invoice-item {
  border-bottom: 1px solid #e9ecef;
  padding: 12px 8px;
  transition: background-color 0.2s ease;
  border-radius: 4px;
  margin-bottom: 4px;
}

.invoice-item:hover {
  background-color: #f8f9fa;
}

.invoice-item:last-child {
  border-bottom: none;
}

/* Quantity controls */
.invoice-item .btn-sm {
  width: 30px;
  height: 30px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Price display */
.invoice-item .text-primary {
  font-size: 1.1em;
  font-weight: 700;
}

/* Button improvements */
.btn-block {
  margin-bottom: 8px;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.btn-block:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Summary section styling */
.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  min-height: 32px;
}

.summary-row.total-row {
  font-size: 1.1em;
  margin: 8px 0;
}

.summary-input {
  width: 80px;
  height: 32px;
  padding: 4px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  text-align: right;
  font-size: 14px;
}

.summary-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  outline: none;
}

/* Action buttons styling */
.action-buttons-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.action-btn-compact {
  width: 100%;
  height: 38px;
  font-weight: 600;
  border-radius: 6px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.action-btn-compact:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.action-btn-compact:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Search input improvements */
.form-control-lg {
  border-radius: 8px;
  border: 2px solid #e9ecef;
  transition: border-color 0.3s ease;
}

.form-control-lg:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Mobile responsive */
@media (max-width: 768px) {
  .content-section .col-md-2,
  .content-section .col-md-7,
  .content-section .col-md-3 {
    height: auto;
    min-height: 300px;
  }

  .header-section h3 { /* Changed from h2 to h3 to match HTML */
    font-size: 1.5rem;
  }

  .card {
    min-height: 60px;
  }
}
