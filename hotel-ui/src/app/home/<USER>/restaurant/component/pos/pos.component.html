<div class="container-fluid mt-3 px-0 p-1">
  <!-- Professional POS Header -->
  <div class="professional-card mb-3 animate-fade-in">
    <div class="professional-card-header bg-gradient-primary">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h3 class="professional-card-title mb-0 text-white">Viganana POS</h3>
          <p class="text-white-50 mb-0">Point of Sale System</p>
        </div>
        <div class="d-none d-md-flex gap-2">
          <button class="btn btn-professional btn-professional-outline btn-outline-light btn-professional-sm"
                  (click)="openTableSelection()" title="Select Table">
            <i class="fas fa-table me-1"></i>Tables
          </button>
          <button class="btn btn-professional btn-professional-outline btn-outline-light btn-professional-sm"
                  (click)="showCategories()" title="Categories">
            <i class="fas fa-list me-1"></i>Menu
          </button>
          <button class="btn btn-professional btn-professional-outline btn-outline-light btn-professional-sm"
                  routerLink="../home/<USER>/manage_sales_invoices" title="Sales History">
            <i class="fas fa-history me-1"></i>History
          </button>
          <button class="btn btn-professional btn-professional-outline btn-outline-light btn-professional-sm"
                  routerLink="../home/<USER>" (click)="closeFullscreen()" title="Home">
            <i class="fas fa-home me-1"></i>Home
          </button>
        </div>
      </div>

      <!-- Mobile action buttons -->
      <div class="d-flex justify-content-between mt-3 d-md-none">
        <button class="btn btn-professional btn-professional-outline btn-outline-light btn-professional-sm px-2"
                (click)="openTableSelection()">
          <i class="fas fa-table"></i>
        </button>
        <button class="btn btn-professional btn-professional-outline btn-outline-light btn-professional-sm px-2"
                (click)="showCategories()">
          <i class="fas fa-list"></i>
        </button>
        <button class="btn btn-professional btn-professional-outline btn-outline-light btn-professional-sm px-2"
                routerLink="../home/<USER>/manage_sales_invoices">
          <i class="fas fa-history"></i>
        </button>
        <button class="btn btn-professional btn-professional-outline btn-outline-light btn-professional-sm px-2"
                routerLink="../home/<USER>" (click)="closeFullscreen()">
          <i class="fas fa-home"></i>
        </button>
      </div>
    </div>
  </div>

  <!-- Content -->
  <div class="content-section overflow-auto">
    <div class="row no-gutters">
      <!-- Orders -->
      <div class="col-md-2 col-sm-12 border-right p-3 bg-white">
        <h5 class="text-primary mb-3">Orders</h5>
        <ul class="list-group list-group-flush overflow-auto">
          <li class="list-group-item list-group-item-action"
              *ngFor="let ordr of waitingOrders; let i = index"
              (click)="selectRecord(ordr, i)"
              [class.active]="i === selectedRow">
            <div class="d-flex justify-content-between">
              <span>{{ ordr.orderNo }}</span>
              <small class="text-muted">{{ ordr.tableNo }}</small>
            </div>
          </li>
        </ul>
      </div>

      <!-- Main Section -->
      <div class="col-md-7 col-sm-12 p-3">
        <!-- Search Inputs -->
        <div class="form-row">
          <div class="col-md-4">
            <input #barcodeElm
                   [(ngModel)]="keyBarcodeSearch"
                   [typeahead]="itemSearchList"
                   (typeaheadLoading)="searchItemsByBarcode()"
                   (typeaheadOnSelect)="setSearchedSelectedItem($event)"
                   [typeaheadOptionsLimit]="7"
                   typeaheadWaitMs="1000"
                   typeaheadOptionField="barcode"
                   class="form-control form-control-lg"
                   autocomplete="off"
                   placeholder="Barcode"
                   name="searchItem">
          </div>
          <div class="col-md-4">
            <input #itemName
                   [(ngModel)]="keyItemNameSearch"
                   [typeahead]="itemNameSearchList"
                   (typeaheadLoading)="searchItemsByName()"
                   (typeaheadOnSelect)="setSearchedSelectedItem($event)"
                   [typeaheadOptionsLimit]="10"
                   typeaheadWaitMs="1000"
                   [typeaheadItemTemplate]="customItemTemplate"
                   typeaheadOptionField="itemName"
                   class="form-control form-control-lg"
                   autocomplete="off"
                   placeholder="Item Name"
                   name="searchItem">
            <ng-template #customItemTemplate let-model="item">
              <span><strong>{{model.itemName}}</strong> - {{model.sellingPrice}}</span>
            </ng-template>
          </div>
          <div class="col-md-2">
            <input #sellingPrice type="number" class="form-control form-control-lg" placeholder="Price" [(ngModel)]="sPrice">
          </div>
          <div class="col-md-2">
            <input #quantity type="number" class="form-control form-control-lg" placeholder="Qty" [(ngModel)]="itemQty" (keydown.enter)="addToInvoice()">
          </div>
        </div>

        <!-- View Sections -->
        <div class="overflow-auto" style="height: calc(100vh - 180px);">
          <!-- Categories -->
          <div class="row" *ngIf="isCategory">
            <div class="col-md-3 mb-3" *ngFor="let category of categories">
              <div class="card text-center p-3 cursor-pointer" (click)="selectCategory(category)">
                <i class="fa fa-folder fa-2x mb-2"></i>
                <div>{{ category.categoryName }}</div>
              </div>
            </div>
          </div>

          <!-- SubCategories -->
          <div class="row" *ngIf="isSubCategory">
            <div class="col-md-3 mb-3" *ngFor="let subCategory of subCategories">
              <div class="card text-center p-3 cursor-pointer" (click)="selectSubCategory(subCategory)">
                <i class="fa fa-folder-open fa-2x mb-2"></i>
                <div>{{ subCategory.subCategoryName }}</div>
              </div>
            </div>
            <div class="col-12 text-center mt-3">
              <button class="btn btn-secondary" (click)="showCategories()">
                <i class="fa fa-arrow-left"></i> Back to Categories
              </button>
            </div>
          </div>

          <!-- Items -->
          <div class="row" *ngIf="!isCategory && !isSubCategory">
            <div class="col-md-3 mb-3" *ngFor="let item of items">
              <div class="card text-center p-3 cursor-pointer" (click)="setItem(item)">
                <i class="fa fa-shopping-cart fa-2x mb-2"></i>
                <div>{{ item.itemName }}</div>
                <small class="text-muted">Rs: {{ item.sellingPrice | number: '1.2-2' }}</small>
              </div>
            </div>
            <div class="col-12 text-center mt-3">
              <button class="btn btn-secondary" (click)="showSubCategories()">
                <i class="fa fa-arrow-left"></i> Back to SubCategories
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Invoice Side -->
      <div class="col-md-3 col-sm-12 d-flex flex-column p-3 bg-light border-left">
        <!-- Table Selection -->
        <div class="mb-3 p-3" [ngClass]="selectedTable?.tableNo ? 'table-selection' : 'table-not-selected'">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <i class="fa fa-table mr-2"></i>
              <span class="font-weight-bold">{{ selectedTable?.tableNo || 'Select Table' }}</span>
            </div>
            <button class="btn btn-sm btn-light" (click)="openTableSelection()">
              <i class="fa fa-edit"></i>
            </button>
          </div>
        </div>



        <!-- Items List -->
        <div class="flex-grow-1 overflow-auto">
          <div *ngFor="let rec of invoice?.salesInvoiceRecords; let i = index" class="invoice-item">
            <!-- Item Info Row -->
            <div class="d-flex justify-content-between align-items-center mb-2">
              <div class="flex-grow-1">
                <div class="font-weight-bold">{{ rec.itemName }}</div>
                <small class="text-muted">{{ rec.item?.barcode || 'No barcode' }}</small>
              </div>
              <button class="btn btn-sm btn-outline-danger" (click)="remove(i)">
                <i class="fa fa-trash"></i>
              </button>
            </div>

            <!-- Quantity and Price Row -->
            <div class="d-flex justify-content-between align-items-center">
              <div class="d-flex align-items-center">
                <button class="btn btn-sm btn-outline-danger mr-1" (click)="decreaseQuantity(i, 'displayQuantity', 'quantity', rec)">
                  <i class="fa fa-minus"></i>
                </button>
                <span class="px-2 font-weight-bold">{{ rec.displayQuantity }}</span>
                <button class="btn btn-sm btn-outline-success" (click)="increaseQuantity(i, 'displayQuantity', 'quantity', rec)">
                  <i class="fa fa-plus"></i>
                </button>
              </div>
              <div class="text-right">
                <div class="font-weight-bold text-primary">Rs {{ rec.price | number:'1.2-2' }}</div>
                <small class="text-muted" *ngIf="rec.item?.ticketType?.value">
                  <i class="fa fa-tag mr-1"></i>{{ rec.item.ticketType.value }}
                </small>
              </div>
            </div>
          </div>
          <div *ngIf="invoice?.salesInvoiceRecords?.length === 0" class="text-center text-muted py-4">
            <i class="fa fa-shopping-cart fa-3x mb-3"></i>
            <p>No items added yet</p>
            <small>Select items from categories or search by name/barcode</small>
          </div>
        </div>

        <!-- Invoice Summary -->
        <div class="mt-3">
          <div class="summary-row">
            <span>Sub Total</span>
            <strong>{{ invoice.subTotal | number:'1.2-2' }}</strong>
          </div>
          <div class="summary-row">
            <span>Service Charge</span>
            <input type="number" class="summary-input" [(ngModel)]="invoice.serviceCharge" (ngModelChange)="calculateTotal()">
          </div>
          <hr class="my-2">
          <div class="summary-row total-row">
            <strong>Total</strong>
            <strong>{{ invoice.totalAmount | number:'1.2-2' }}</strong>
          </div>
          <div class="summary-row">
            <span>Payment</span>
            <input #payment type="number" class="summary-input" [(ngModel)]="invoice.payment" (ngModelChange)="calculateBalance()">
          </div>
          <div class="summary-row">
            <span>Balance</span>
            <strong>{{ invoice.cashBalance | number:'1.2-2' }}</strong>
          </div>
          <!-- Payment Method -->
          <div class="d-flex justify-content-end mb-2">
            <div class="form-check form-check-inline mr-3">
              <input class="form-check-input" type="radio" name="paymentMethod" id="cash" value="Cash" [(ngModel)]="paymentMethodName" [checked]="true">
              <label class="form-check-label" for="cash">Cash</label>
            </div>
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="paymentMethod" id="card" value="Card" [(ngModel)]="paymentMethodName">
              <label class="form-check-label" for="card">Card</label>
            </div>
          </div>

          <!-- Action Buttons - Compact Layout -->
          <div class="action-buttons-container">
            <button class="btn btn-info action-btn-compact" (click)="openModalSelectTable()" [disabled]="!(invoice?.salesInvoiceRecords?.length > 0)">
              <i class="fa fa-plus-circle mr-1"></i>Add To Order
            </button>
            <button class="btn btn-secondary action-btn-compact" (click)="printOnly()" [disabled]="!(invoice?.salesInvoiceRecords?.length > 0)">
              <i class="fa fa-print mr-1"></i>Print
            </button>
            <button class="btn btn-primary action-btn-compact" (click)="save(true)" [disabled]="!(invoice?.salesInvoiceRecords?.length > 0) || !ableToSaveInvoice">
              <i class="fa fa-check-circle mr-1"></i>Checkout
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
