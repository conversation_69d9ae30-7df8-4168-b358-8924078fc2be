import {Component, ElementRef, HostListener, OnInit, ViewChild} from '@angular/core';
import {Company} from "../../../../core/model/company";
import {CompanyService} from "../../../../core/service/company.service";
import {BsModalRef} from "ngx-bootstrap/modal";
import {MetaData} from "../../../../core/model/metaData";
import {Order} from "../../model/order";
import {OrderService} from "../../service/order.service";
import {SalesInvoiceRecord} from "../../../trade/model/sales-invoice-record";

@Component({
  selector: 'app-invoice',
  templateUrl: './ticket.component.html',
  styleUrls: ['./ticket.component.css']
})
export class TicketComponent implements OnInit {

  order: Order;
  orderId: string;
  date: Date;
  existingCss: boolean;
  company: Company;
  user: string;
  modalRef: BsModalRef;
  ticketType: string;
  kotRecords: Array<SalesInvoiceRecord>;
  botRecords: Array<SalesInvoiceRecord>;

  @ViewChild('printBtn') printBtn: ElementRef;

  constructor(private orderService: OrderService, private companyService: CompanyService) {
  }

  ngOnInit(): void {
    this.order = new Order();
    this.company = new Company();
    this.order.salesType = null;
    this.date = new Date(Date.now());
    this.existingCss = true;
    this.kotRecords = [];
    this.botRecords = [];
    this.user = JSON.parse(localStorage.getItem('currentUser')).user.firstName;
  }

  findCompany() {
    this.companyService.findCompany().subscribe((data: Company) => {
      this.company = data;
    });
  }

  findOrder() {
    this.findCompany();
    this.orderService.findAllByOrderId(this.orderId).subscribe((result: Order) => {
      this.order = result;
      this.kotRecords = [];
      this.botRecords = [];
      for (let ord of this.order.salesInvoiceRecords) {
        if (ord.item && ord.item.ticketType && ord.item.ticketType.value) {
          if (ord.item.ticketType.value === 'KOT' && !ord.printed) {
            this.kotRecords.push(ord);
          }
          if (ord.item.ticketType.value === 'BOT' && !ord.printed) {
            this.botRecords.push(ord);
          }
          ord.printed = true;
        }
      }
    })
  }

  @HostListener('window:keydown', ['$event'])
  keyEvent(event: KeyboardEvent) {
    if (event.key == 'Enter') {
      this.printBtn.nativeElement.click();
      this.modalRef.hide();
    }
  }

}
