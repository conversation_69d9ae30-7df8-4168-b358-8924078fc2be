import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {ApiConstants} from '../restaurant-constants';


@Injectable({
  providedIn: 'root'
})
export class OrderService {

  constructor(private http: HttpClient) {
  }

  public save(order) {
    return this.http.post<any>(ApiConstants.SAVE_ORDER, order);
  }

  public findAll() {
    return this.http.get(ApiConstants.GET_ORDERS);
  }

  public findActive() {
    return this.http.get(ApiConstants.GET_ACTIVE_ORDERS);
  }

  public findAllByOrderId(orderId) {
    return this.http.get(ApiConstants.FIND_ALL_BY_ORDER_ID, {params: {id: orderId}});
  }

  public delete(orderId) {
    return this.http.delete(ApiConstants.DELETE_ORDER, {params: {id: orderId}});
  }

}
