<div class="container-fluid mt-3">
  <div class="row g-4">
    <!-- Warehouse Form Section -->
    <div class="col-lg-4">
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <h5 class="card-title mb-0">
            {{ warehouse.id ? 'Edit Warehouse' : 'Warehouse Details' }}
          </h5>
        </div>

        <div class="card-body">
          <form #manageWarehouseForm="ngForm" (ngSubmit)="saveWarehouse()">

            <!-- Warehouse Name Field -->
            <div class="form-group mb-4">
              <label class="form-label fw-semibold">Warehouse Name</label>
              <input type="text"
                     class="form-control form-control-lg"
                     [(ngModel)]="warehouse.name"
                     name="warehouseName"
                     placeholder="Enter warehouse name"
                     required>
            </div>

            <!-- Address Field -->
            <div class="form-group mb-4">
              <label class="form-label fw-semibold">Address</label>
              <textarea class="form-control"
                        rows="2"
                        [(ngModel)]="warehouse.address"
                        name="address"
                        placeholder="Enter warehouse address"></textarea>
            </div>

            <!-- Contact Information -->
            <div class="row">
              <div class="col-6">
                <div class="form-group mb-4">
                  <label class="form-label fw-semibold">Telephone</label>
                  <input type="tel"
                         class="form-control"
                         [(ngModel)]="warehouse.telephone1"
                         name="telephone"
                         placeholder="Phone number">
                </div>
              </div>
              <div class="col-6">
                <div class="form-group mb-4">
                  <label class="form-label fw-semibold">Extra Data</label>
                  <input type="text"
                         class="form-control"
                         [(ngModel)]="warehouse.param1"
                         name="extraData"
                         placeholder="Additional info">
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="d-grid gap-2">
              <button type="submit" class="btn btn-primary btn-lg">
                {{ warehouse.id ? 'Update Warehouse' : 'Save Warehouse' }}
              </button>
              <button type="button" class="btn btn-outline-secondary" (click)="clearAll()">
                Clear
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Warehouses List Section -->
    <div class="col-lg-8">
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Warehouses</h5>
            <div class="search-container">
              <div class="input-group">
                <span class="input-group-text bg-light border-end-0">
                  <i class="fas fa-search text-muted"></i>
                </span>
                <input [(ngModel)]="keyWarehouse"
                       [typeahead]="warehouses"
                       (typeaheadLoading)="loadWarehouses()"
                       (typeaheadOnSelect)="setSelectedWarehouse($event)"
                       [typeaheadOptionsLimit]="7"
                       typeaheadWaitMs="1000"
                       typeaheadOptionField="name"
                       placeholder="Search warehouses..."
                       autocomplete="off"
                       class="form-control border-start-0"
                       name="warehouseSearch">
              </div>
            </div>
          </div>
        </div>

        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th class="border-0 fw-semibold">Warehouse Name</th>
                  <th class="border-0 fw-semibold">Address</th>
                  <th class="border-0 fw-semibold">Contact</th>
                  <th class="border-0 fw-semibold text-center">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let warehouse of warehouses; let i = index"
                    class="table-row"
                    [class.table-active]="i === selectedRow"
                    (click)="selectWarehouse(warehouse, i)">
                  <td class="align-middle">
                    <span class="entity-name">{{ warehouse.name }}</span>
                  </td>
                  <td class="align-middle">
                    <span class="text-muted">{{ warehouse.address || 'No address' }}</span>
                  </td>
                  <td class="align-middle">
                    <span class="text-muted">{{ warehouse.telephone1 || 'No contact' }}</span>
                  </td>
                  <td class="align-middle text-center">
                    <button class="btn btn-sm btn-outline-primary"
                            (click)="selectWarehouse(warehouse, i); $event.stopPropagation()">
                      <i class="fas fa-edit"></i>
                    </button>
                  </td>
                </tr>
                <tr *ngIf="warehouses?.length === 0">
                  <td colspan="4" class="text-center py-4 text-muted">
                    <i class="fas fa-warehouse fa-2x mb-3 d-block"></i>
                    No warehouses found
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div class="card-footer bg-white border-top" *ngIf="collectionSize > pageSize">
          <div class="d-flex justify-content-center">
            <pagination class="pagination-sm"
                        [totalItems]="collectionSize"
                        [(ngModel)]="page"
                        [boundaryLinks]="true"
                        [maxSize]="5"
                        (pageChanged)="pageChanged($event)"
                        [ngModelOptions]="{standalone: true}">
            </pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
