<div class="container-fluid mt-3" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <!-- <PERSON><PERSON> Close Button (only for modal mode) -->
  <div *ngIf="isModal" class="d-flex justify-content-end mb-3">
    <button type="button" class="btn-close" aria-label="Close" (click)="closeModal()"></button>
  </div>

  <div class="row g-4">
    <!-- Sub Categories List Section -->
    <div class="col-lg-8">
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Sub Categories</h5>
            <div class="search-container">
              <div class="input-group">
                <span class="input-group-text bg-light border-end-0">
                  <i class="fas fa-search text-muted"></i>
                </span>
                <input [(ngModel)]="keySubCategory"
                       [typeahead]="subCategoriesSearched"
                       (typeaheadLoading)="loadSubCategories()"
                       (typeaheadOnSelect)="setSelectedSubCategory($event)"
                       [typeaheadOptionsLimit]="7"
                       typeaheadWaitMs="1000"
                       typeaheadOptionField="subCategoryName"
                       placeholder="Search sub categories..."
                       autocomplete="off"
                       class="form-control border-start-0"
                       name="subCategorySearch">
              </div>
            </div>
          </div>
        </div>

        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th class="border-0 fw-semibold">Sub Category Name</th>
                  <th class="border-0 fw-semibold">Parent Category</th>
                  <th class="border-0 fw-semibold text-center">Status</th>
                  <th class="border-0 fw-semibold text-center">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let subCat of subItemCategories; let i = index"
                    class="table-row"
                    [class.table-active]="subCat === selectedSubCategory"
                    (click)="onSelect(subCat)">
                  <td class="align-middle">
                    <span class="entity-name">{{ subCat.subCategoryName }}</span>
                  </td>
                  <td class="align-middle">
                    {{ subCat.itemCategory?.categoryName || 'N/A' }}
                  </td>
                  <td class="align-middle text-center">
                    <span class="badge" [class]="subCat.active ? 'badge-success' : 'badge-danger'">
                      <i class="fas" [class]="subCat.active ? 'fa-check-circle' : 'fa-times-circle'"></i>
                      {{ subCat.active ? 'Active' : 'Inactive' }}
                    </span>
                  </td>
                  <td class="align-middle text-center">
                    <button class="btn btn-sm btn-outline-primary"
                            (click)="onSelect(subCat); $event.stopPropagation()">
                      <i class="fas fa-edit"></i>
                    </button>
                  </td>
                </tr>
                <tr *ngIf="subItemCategories?.length === 0">
                  <td colspan="4" class="text-center py-4 text-muted">
                    <i class="fas fa-layer-group fa-2x mb-3 d-block"></i>
                    No sub categories found
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div class="card-footer bg-white border-top" *ngIf="collectionSize > pageSize">
          <div class="d-flex justify-content-center">
            <pagination class="pagination-sm"
                        [totalItems]="collectionSize"
                        [(ngModel)]="page"
                        [boundaryLinks]="true"
                        [maxSize]="5"
                        (pageChanged)="pageChanged($event)">
            </pagination>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-6">

      <form (ngSubmit)="save();ManageSubItemCategoryForm.reset()" #ManageSubItemCategoryForm="ngForm">

        <div class="form-group pl-0">
          <label>Sub Category Name</label>
          <input type="text" required #subName="ngModel"
                 class="form-control" id="subName" [(ngModel)]="subCategory.subCategoryName"
                 name="subName"
                 placeholder=" Sub Category Name">
          <div *ngIf="subName.errors && (subName.invalid || subName.touched)">
            <small class="text-danger" [class.d-none]="subName.valid || subName.untouched">*item
              Sub Category Name is required
            </small>
          </div>
        </div>
        <div class="form-group pl-0">
          <label>Item Category</label>
          <select class="form-control" required #category="ngModel" (change)="onChange($event)"
                  [class.is-invalid]="category.invalid && category.touched"
                  name="itemCategory" [(ngModel)]="categoryId">
            <option [value]="undefined" disabled> Choose Item Category</option>
            <option *ngFor="let itemCategory of itemCategories" [value]="itemCategory.id">
              {{itemCategory.categoryName}}
            </option>
          </select>
          <small class="text-danger" [class.d-none]="category.valid || category.untouched">*itemCategory is
            required
          </small>
        </div>
        <div class="form-check checkbox mr-2 mb-3 mt-3">
          <input class="form-check-input" id="check3" name="check3" type="checkbox" value=""
                 [(ngModel)]="subCategory.active">
          <label class="form-check-label" for="check3">Active</label>
        </div>
        <div class="btn-block text-right">
          <button class="btn btn-primary m-1 mr-5"
                  [disabled]="selectedSubCategory!=null||!ManageSubItemCategoryForm.form.valid">save
          </button>
          <button type="button" class="btn btn-primary m-1"
                  [disabled]="selectedSubCategory===null"
                  (click)="openModal(templateUpdate)">update
          </button>
          <button type="button" class="btn btn-danger m-1" [disabled]="selectedSubCategory===null"
                  (click)="openModal(template)">delete
          </button>
          <button type="button" (click)="clear()" class="btn btn-warning m-1">clear</button>
        </div>
      </form>
    </div>
  </div>
</div>


<ng-template #template>
  <div class="modal-header">
    <h4 class="modal-title pull-left">Confirmation</h4>
    <button type="button" class="close pull-right" aria-label="Close" (click)="decline()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body text-center ">
    <p>Do you want to confirm?</p>
    <button type="button" class="btn btn-default" (click)="confirmDelete()">Yes</button>
    <button type="button" class="btn btn-primary" (click)="decline()">No</button>
  </div>
</ng-template>

<ng-template #templateUpdate>
  <div class="modal-header">
    <h4 class="modal-title pull-left">Confirmation</h4>
    <button type="button" class="close pull-right" aria-label="Close" (click)="decline()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body text-center ">
    <p>Do you want to confirm?</p>
    <button type="button" class="btn btn-default" (click)="confirmUpdate()">Yes</button>
    <button type="button" class="btn btn-primary" (click)="decline()">No</button>
  </div>
</ng-template>





