<div class="container-fluid mt-3" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <!-- <PERSON><PERSON> Close Button (only for modal mode) -->
  <div *ngIf="isModal" class="d-flex justify-content-end mb-3">
    <button type="button" class="btn-close" aria-label="Close" (click)="closeModal()"></button>
  </div>

  <div class="row g-4">
    <!-- Item Types List Section -->
    <div class="col-lg-8">
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Item Types</h5>
            <div class="search-container">
              <div class="input-group">
                <span class="input-group-text bg-light border-end-0">
                  <i class="fas fa-search text-muted"></i>
                </span>
                <input [(ngModel)]="keyItemType"
                       [typeahead]="itemTypesSearched"
                       (typeaheadLoading)="loadItemCategories()"
                       (typeaheadOnSelect)="setSelectedItemCategory($event)"
                       [typeaheadOptionsLimit]="7"
                       typeaheadWaitMs="1000"
                       typeaheadOptionField="name"
                       placeholder="Search item types..."
                       autocomplete="off"
                       class="form-control border-start-0"
                       name="itemTypeSearch">
              </div>
            </div>
          </div>
        </div>

        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th class="border-0 fw-semibold">Item Type Name</th>
                  <th class="border-0 fw-semibold">Description</th>
                  <th class="border-0 fw-semibold text-center">Status</th>
                  <th class="border-0 fw-semibold text-center">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let itemType of itemTypes; let i = index"
                    class="table-row"
                    [class.table-active]="itemType === selectedItem"
                    (click)="onSelect(itemType)">
                  <td class="align-middle">
                    <span class="entity-name">{{ itemType.name }}</span>
                  </td>
                  <td class="align-middle">
                    <span class="text-muted">{{ itemType.description || 'No description' }}</span>
                  </td>
                  <td class="align-middle text-center">
                    <span class="badge" [class]="itemType.active ? 'badge-success' : 'badge-danger'">
                      <i class="fas" [class]="itemType.active ? 'fa-check-circle' : 'fa-times-circle'"></i>
                      {{ itemType.active ? 'Active' : 'Inactive' }}
                    </span>
                  </td>
                  <td class="align-middle text-center">
                    <button class="btn btn-sm btn-outline-primary"
                            (click)="onSelect(itemType); $event.stopPropagation()">
                      <i class="fas fa-edit"></i>
                    </button>
                  </td>
                </tr>
                <tr *ngIf="itemTypes?.length === 0">
                  <td colspan="4" class="text-center py-4 text-muted">
                    <i class="fas fa-cube fa-2x mb-3 d-block"></i>
                    No item types found
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div class="card-footer bg-white border-top" *ngIf="collectionSize > pageSize">
          <div class="d-flex justify-content-center">
            <pagination class="pagination-sm"
                        [totalItems]="collectionSize"
                        [(ngModel)]="page"
                        [boundaryLinks]="true"
                        [maxSize]="5"
                        (pageChanged)="pageChanged($event)">
            </pagination>
          </div>
        </div>
      </div>
    </div>
    <!-- Item Type Form Section -->
    <div class="col-lg-4">
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <h5 class="card-title mb-0">
            {{ selectedItem ? 'Edit Item Type' : 'Item Type Details' }}
          </h5>
        </div>

        <div class="card-body">
          <form #ManageItemTypeForm="ngForm" (ngSubmit)="save()">

            <!-- Item Type Name Field -->
            <div class="form-group mb-4">
              <label class="form-label fw-semibold">Item Type Name</label>
              <input type="text"
                     class="form-control form-control-lg"
                     [(ngModel)]="itemType.name"
                     name="itemTypeName"
                     placeholder="Enter item type name"
                     required>
            </div>

            <!-- Description Field -->
            <div class="form-group mb-4">
              <label class="form-label fw-semibold">Description</label>
              <textarea class="form-control"
                        rows="3"
                        [(ngModel)]="itemType.description"
                        name="description"
                        placeholder="Enter description (optional)"></textarea>
            </div>

            <!-- Status Toggle -->
            <div class="form-group mb-4">
              <label class="form-label fw-semibold mb-3">Item Type Settings</label>

              <div class="status-toggles">
                <div class="form-check form-switch">
                  <input class="form-check-input"
                         type="checkbox"
                         id="activeSwitch"
                         [(ngModel)]="itemType.active"
                         name="active">
                  <label class="form-check-label fw-semibold" for="activeSwitch">
                    Active Status
                  </label>
                  <div class="form-text">Enable this item type for use</div>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="d-grid gap-2">
              <button type="submit" class="btn btn-primary btn-lg"
                      [disabled]="selectedItem != null || !ManageItemTypeForm.form.valid">
                Save Item Type
              </button>
              <button type="button" class="btn btn-primary ml-1"
                      [disabled]="selectedItem === null"
                      (click)="openModal(templateUpdate)">
                Update Item Type
              </button>
              <button type="button" class="btn btn-outline-secondary ml-1" (click)="clear()">
                Clear
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #templateUpdate>
  <div class="modal-body text-center">
    <div class="mb-3">
      <i class="fas fa-question-circle fa-3x text-primary"></i>
    </div>
    <h5 class="mb-3">Confirm Update</h5>
    <p class="mb-4">Do you want to update this item type?</p>
    <div class="d-flex justify-content-center gap-2">
      <button type="button" class="btn btn-primary" (click)="confirmUpdate()">
        Yes, Update
      </button>
      <button type="button" class="btn btn-outline-secondary ml-1" (click)="decline()">
        Cancel
      </button>
    </div>
  </div>
</ng-template>




