<div class="container-fluid mt-3" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <!-- <PERSON><PERSON> Close Button (only for modal mode) -->
  <div *ngIf="isModal" class="d-flex justify-content-end mb-3">
    <button type="button" class="btn-close" aria-label="Close" (click)="closeModal()"></button>
  </div>

  <div class="row g-4">
    <!-- UOM List Section -->
    <div class="col-lg-8">
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Units of Measure</h5>
            <div class="search-container">
              <div class="input-group">
                <span class="input-group-text bg-light border-end-0">
                  <i class="fas fa-search text-muted"></i>
                </span>
                <input type="text"
                       class="form-control border-start-0"
                       placeholder="Search units..."
                       name="uomSearch">
              </div>
            </div>
          </div>
        </div>

        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th class="border-0 fw-semibold">Unit Name</th>
                  <th class="border-0 fw-semibold">Symbol</th>
                  <th class="border-0 fw-semibold text-center">Type</th>
                  <th class="border-0 fw-semibold text-center">Status</th>
                  <th class="border-0 fw-semibold text-center">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let unit of units; let i = index"
                    class="table-row"
                    [class.table-active]="i === selectedRow"
                    (click)="uomDetail(unit, i)">
                  <td class="align-middle">
                    <span class="entity-name">{{ unit.name }}</span>
                  </td>
                  <td class="align-middle">
                    <span class="badge badge-secondary">{{ unit.symbol }}</span>
                  </td>
                  <td class="align-middle text-center">
                    <span class="badge" [class]="unit.isWholeNumber ? 'badge-primary' : 'badge-info'">
                      {{ unit.isWholeNumber ? 'Whole Number' : 'Decimal' }}
                    </span>
                  </td>
                  <td class="align-middle text-center">
                    <span class="badge" [class]="unit.active ? 'badge-success' : 'badge-danger'">
                      <i class="fas" [class]="unit.active ? 'fa-check-circle' : 'fa-times-circle'"></i>
                      {{ unit.active ? 'Active' : 'Inactive' }}
                    </span>
                  </td>
                  <td class="align-middle text-center">
                    <button class="btn btn-sm btn-outline-primary"
                            (click)="uomDetail(unit, i); $event.stopPropagation()">
                      <i class="fas fa-edit"></i>
                    </button>
                  </td>
                </tr>
                <tr *ngIf="units?.length === 0">
                  <td colspan="5" class="text-center py-4 text-muted">
                    <i class="fas fa-ruler fa-2x mb-3 d-block"></i>
                    No units found
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div class="card-footer bg-white border-top" *ngIf="collectionSize > pageSize">
          <div class="d-flex justify-content-center">
            <pagination class="pagination-sm"
                        [totalItems]="collectionSize"
                        [(ngModel)]="page"
                        [boundaryLinks]="true"
                        [maxSize]="5"
                        (pageChanged)="pageChanged($event)"
                        [ngModelOptions]="{standalone: true}">
            </pagination>
          </div>
        </div>
      </div>
    </div>

    <!-- UOM Form Section -->
    <div class="col-lg-4">
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <h5 class="card-title mb-0">
            {{ uom.id ? 'Edit Unit' : 'Unit Details' }}
          </h5>
        </div>

        <div class="card-body">
          <form #UOMForm="ngForm" (ngSubmit)="saveUOM()">

            <!-- Unit Name Field -->
            <div class="form-group mb-4">
              <label class="form-label fw-semibold">Unit Name</label>
              <input type="text"
                     class="form-control form-control-lg"
                     [(ngModel)]="uom.name"
                     name="unitName"
                     placeholder="Enter unit name"
                     required>
            </div>

            <!-- Symbol Field -->
            <div class="form-group mb-4">
              <label class="form-label fw-semibold">Symbol</label>
              <input type="text"
                     class="form-control form-control-lg"
                     [(ngModel)]="uom.symbol"
                     name="symbol"
                     placeholder="Enter symbol"
                     required>
            </div>

            <!-- Settings -->
            <div class="form-group mb-4">
              <label class="form-label fw-semibold mb-3">Unit Settings</label>

              <div class="status-toggles">
                <div class="form-check form-switch mb-3">
                  <input class="form-check-input"
                         type="checkbox"
                         id="wholeNumberSwitch"
                         [(ngModel)]="uom.isWholeNumber"
                         name="isWholeNumber">
                  <label class="form-check-label fw-semibold" for="wholeNumberSwitch">
                    Whole Number Only
                  </label>
                  <div class="form-text">Restrict to whole numbers only</div>
                </div>

                <div class="form-check form-switch">
                  <input class="form-check-input"
                         type="checkbox"
                         id="activeSwitch"
                         [(ngModel)]="uom.active"
                         name="active">
                  <label class="form-check-label fw-semibold" for="activeSwitch">
                    Active Status
                  </label>
                  <div class="form-text">Enable this unit for use</div>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="d-grid gap-2">
              <button type="submit" class="btn btn-primary btn-lg">
                {{ uom.id ? 'Update Unit' : 'Save Unit' }}
              </button>
              <button type="button" class="btn btn-outline-secondary" (click)="clearAll()">
                Clear
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
