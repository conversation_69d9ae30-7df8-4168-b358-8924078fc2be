<div class="container-fluid mt-3" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <!-- <PERSON><PERSON> Close Button (only for modal mode) -->
  <div *ngIf="isModal" class="d-flex justify-content-end mb-3">
    <button type="button" class="btn-close" aria-label="Close" (click)="closeModal()"></button>
  </div>

  <div class="row g-4">
    <!-- Brands List Section -->
    <div class="col-lg-8">
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Brands</h5>
            <div class="search-container">
              <div class="input-group">
                <span class="input-group-text bg-light border-end-0">
                  <i class="fas fa-search text-muted"></i>
                </span>
                <input [(ngModel)]="keyBrand"
                       [typeahead]="brands"
                       (typeaheadLoading)="loadBrands()"
                       (typeaheadOnSelect)="setSelectedBrand($event)"
                       [typeaheadOptionsLimit]="7"
                       typeaheadWaitMs="1000"
                       typeaheadOptionField="name"
                       placeholder="Search brands..."
                       autocomplete="off"
                       class="form-control border-start-0"
                       name="brandSearch">
              </div>
            </div>
          </div>
        </div>

        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th class="border-0 fw-semibold">Brand Name</th>
                  <th class="border-0 fw-semibold text-center">Status</th>
                  <th class="border-0 fw-semibold text-center">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let brand of brands; let i = index"
                    class="table-row"
                    [class.table-active]="i === selectedRow"
                    (click)="brandDetail(brand, i)">
                  <td class="align-middle">
                    <span class="entity-name">{{ brand.name }}</span>
                  </td>
                  <td class="align-middle text-center">
                    <span class="badge" [class]="brand.active ? 'badge-success' : 'badge-danger'">
                      <i class="fas" [class]="brand.active ? 'fa-check-circle' : 'fa-times-circle'"></i>
                      {{ brand.active ? 'Active' : 'Inactive' }}
                    </span>
                  </td>
                  <td class="align-middle text-center">
                    <button class="btn btn-sm btn-outline-primary"
                            (click)="brandDetail(brand, i); $event.stopPropagation()">
                      <i class="fas fa-edit"></i>
                    </button>
                  </td>
                </tr>
                <tr *ngIf="brands?.length === 0">
                  <td colspan="3" class="text-center py-4 text-muted">
                    <i class="fas fa-inbox fa-2x mb-3 d-block"></i>
                    No brands found
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div class="card-footer bg-white border-top" *ngIf="collectionSize > pageSize">
          <div class="d-flex justify-content-center">
            <pagination class="pagination-sm"
                        [totalItems]="collectionSize"
                        [(ngModel)]="page"
                        [boundaryLinks]="true"
                        [maxSize]="5"
                        (pageChanged)="pageChanged($event)">
            </pagination>
          </div>
        </div>
      </div>
    </div>

    <!-- Brand Form Section -->
    <div class="col-lg-4">
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <h5 class="card-title mb-0">
            {{ brand.id ? 'Edit Brand' : 'Brand Details' }}
          </h5>
        </div>

        <div class="card-body">
          <form #manageBrandForm="ngForm" (ngSubmit)="saveBrand()">

            <!-- Brand Name Field -->
            <div class="form-group mb-4">
              <label class="form-label fw-semibold">Brand Name</label>
              <input type="text"
                     class="form-control form-control-lg"
                     [(ngModel)]="brand.name"
                     name="brandName"
                     placeholder="Enter brand name"
                     required>
            </div>

            <!-- Status Toggle -->
            <div class="form-group mb-4">
              <label class="form-label fw-semibold mb-3">Brand Settings</label>

              <div class="status-toggles">
                <div class="form-check form-switch">
                  <input class="form-check-input"
                         type="checkbox"
                         id="activeSwitch"
                         [(ngModel)]="brand.active"
                         name="active">
                  <label class="form-check-label fw-semibold" for="activeSwitch">
                    Active Status
                  </label>
                  <div class="form-text">Enable this brand for use</div>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="d-grid gap-2">
              <button type="submit" class="btn btn-primary btn-lg">
                {{ brand.id ? 'Update Brand' : 'Save Brand' }}
              </button>
              <button type="button" class="btn btn-outline-secondary" (click)="clear()">
                Clear
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

