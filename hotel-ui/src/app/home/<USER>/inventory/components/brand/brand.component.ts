import {Component, OnInit} from '@angular/core';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {Brand} from '../../model/brand';
import {BrandService} from '../../service/brand.service';
import {NotificationService} from "../../../../core/service/notification.service";

@Component({
  selector: 'app-brand',
  templateUrl: './brand.component.html',
  styleUrls: ['./brand.component.css']
})


export class BrandComponent implements OnInit {

  // Modal support
  modalRef: BsModalRef;
  isModal: boolean = false;

  // Entity management
  brand = new Brand();
  brands: Array<Brand> = [];

  // Search and selection
  keyBrand: string = '';
  selectedRow: number = -1;

  // Pagination
  page: number = 1;
  pageSize: number = 10;
  collectionSize: number = 0;


  constructor(private brandService: BrandService,
              private notificationService: NotificationService) {

  }

  ngOnInit(): void {
    this.initBrand();
    this.findAll();

    // Check if component is opened as modal
    this.isModal = !!this.modalRef;
  }

  /**
   * Initialize brand with default values
   */
  initBrand(): void {
    this.brand = new Brand();
    this.brand.active = true;
    this.selectedRow = -1;
  }

  /**
   * Find all brands with pagination
   */
  findAll(): void {
    this.brandService.findAll(this.page - 1, this.pageSize).subscribe(
      (result: any) => {
        this.brands = result.content || [];
        this.collectionSize = result.totalElements || 0;
      },
      (error) => {
        this.notificationService.showError('Failed to load brands');
        console.error('Find all brands error:', error);
      }
    );
  }

  /**
   * Search brands by name
   */
  loadBrands(): void {
    if (this.keyBrand && this.keyBrand.trim().length > 0) {
      this.brandService.findByName(this.keyBrand).subscribe(
        (data: Array<Brand>) => {
          this.brands = data || [];
        },
        (error) => {
          this.notificationService.showError('Failed to search brands');
          console.error('Search brands error:', error);
        }
      );
    } else {
      this.findAll();
    }
  }

  /**
   * Set selected brand from typeahead
   */
  setSelectedBrand(event: any): void {
    if (event && event.item) {
      this.brandDetail(event.item, -1);
    }
  }

  /**
   * Save brand (create or update)
   */
  saveBrand(): void {
    this.brandService.save(this.brand).subscribe(
      (result: any) => {
        if (result.code === 200) {
          this.notificationService.showSuccess(result.message || 'Brand saved successfully');
          this.findAll();
          this.clear();
        } else {
          this.notificationService.showError(result.message || 'Failed to save brand');
        }
      },
      (error) => {
        this.notificationService.showError('An error occurred while saving the brand');
        console.error('Save brand error:', error);
      }
    );
  }

  /**
   * Handle brand selection from table
   */
  brandDetail(brand: Brand, index: number): void {
    this.brand = { ...brand }; // Create a copy to avoid direct mutation
    this.selectedRow = index;
  }

  /**
   * Clear form and reset selection
   */
  clear(): void {
    this.initBrand();
    this.keyBrand = '';
  }

  /**
   * Handle pagination change
   */
  pageChanged(event: any): void {
    this.page = event.page;
    this.findAll();
  }

  /**
   * Close modal (for modal mode)
   */
  closeModal(): void {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

  /**
   * Set modal reference (for modal mode)
   */
  setModalRef(ref: BsModalRef): void {
    this.modalRef = ref;
    this.isModal = true;
  }

  // Legacy methods for backward compatibility
  updateBrand(): void {
    this.saveBrand();
  }
}
