/* Card Styling */
.card {
  border: none;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
}

.card-header {
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #495057;
}

/* Search Container */
.search-container {
  width: 300px;
}

.input-group-text {
  border: 1px solid #dee2e6;
}

.form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Table Styling */
.table {
  margin-bottom: 0;
}

.table thead th {
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
  padding: 1rem;
}

.table-row {
  cursor: pointer;
  transition: all 0.2s ease;
}

.table-row:hover {
  background-color: #f8f9fa !important;
  transform: scale(1.01);
}

.table-active {
  background-color: #e3f2fd !important;
  border-left: 4px solid #2196f3;
}

.entity-name {
  font-weight: 500;
  color: #495057;
}

/* Badge Styling */
.badge {
  padding: 0.5rem 0.75rem;
  border-radius: 20px;
  font-weight: 500;
  font-size: 0.8rem;
}

.badge-success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
}

.badge-danger {
  background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
  color: white;
}

/* Form Styling */
.form-label {
  color: #495057;
  margin-bottom: 0.75rem;
}

.form-control {
  border-radius: 8px;
  border: 1px solid #dee2e6;
  padding: 0.75rem 1rem;
  transition: all 0.2s ease;
}

.form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-control-lg {
  padding: 1rem 1.25rem;
  font-size: 1.1rem;
}

/* Switch Styling */
.form-check-input:checked {
  background-color: #667eea;
  border-color: #667eea;
}

.form-check-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
}

.status-toggles {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.form-check {
  padding-left: 2rem;
}

.form-check-label {
  color: #495057;
}

/* Button Styling */
.btn {
  border-radius: 8px;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease;
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1.1rem;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-outline-secondary {
  border-color: #6c757d;
  color: #6c757d;
}

.btn-outline-secondary:hover {
  background-color: #6c757d;
  border-color: #6c757d;
  transform: translateY(-1px);
}

.btn-outline-primary {
  border-color: #667eea;
  color: #667eea;
}

.btn-outline-primary:hover {
  background-color: #667eea;
  border-color: #667eea;
  transform: translateY(-1px);
}

.btn-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6c757d;
  cursor: pointer;
}

.btn-close:hover {
  color: #495057;
}

/* Pagination */
.pagination {
  margin-bottom: 0;
}

.page-link {
  border-radius: 6px;
  margin: 0 2px;
  border: 1px solid #dee2e6;
  color: #667eea;
}

.page-link:hover {
  background-color: #667eea;
  border-color: #667eea;
  color: white;
}

.page-item.active .page-link {
  background-color: #667eea;
  border-color: #667eea;
}

/* Responsive Design */
@media (max-width: 768px) {
  .search-container {
    width: 100%;
    margin-top: 1rem;
  }

  .table-responsive {
    font-size: 0.9rem;
  }

  .card-body {
    padding: 1rem;
  }
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card {
  animation: fadeIn 0.5s ease-out;
}

/* Empty State */
.text-muted i {
  color: #adb5bd !important;
}

/* Form Text */
.form-text {
  color: #6c757d;
  font-size: 0.875rem;
}
