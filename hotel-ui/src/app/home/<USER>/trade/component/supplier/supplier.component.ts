import {Component, OnInit} from '@angular/core';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {Supplier} from '../../model/supplier';
import {NotificationService} from '../../../../core/service/notification.service';
import {ResponseHandlerService} from '../../../../core/service/response-handler.service';
import {SupplierService} from "../../service/supplier.service";

@Component({
  selector: 'app-supplier',
  templateUrl: './supplier.component.html',
  styleUrls: ['./supplier.component.css']
})
export class SupplierComponent implements OnInit {

  // Modal support
  modalRef: BsModalRef;
  isModal: boolean = false;

  // Entity management
  supplier = new Supplier();
  suppliers: Array<Supplier> = [];

  // Search and selection
  keySupplier: string = '';
  selectedRow: number = -1;

  // Pagination
  page: number = 1;
  pageSize: number = 10;
  collectionSize: number = 0;

  constructor(private supplierService: SupplierService,
              private notificationService: NotificationService,
              private responseHandler: ResponseHandlerService) {
  }

  ngOnInit(): void {
    this.initSupplier();
    this.findAll();

    // Check if component is opened as modal
    this.isModal = !!this.modalRef;
  }

  /**
   * Initialize supplier with default values
   */
  initSupplier(): void {
    this.supplier = new Supplier();
    this.supplier.active = true;
    this.selectedRow = -1;
  }

  /**
   * Find all suppliers with pagination
   */
  findAll(): void {
    this.supplierService.findAll(this.page - 1, this.pageSize).subscribe(
      (result: any) => {
        this.suppliers = result.content || [];
        this.collectionSize = result.totalElements || 0;
      },
      (error) => {
        this.notificationService.showError('Failed to load suppliers');
        console.error('Find all suppliers error:', error);
      }
    );
  }

  /**
   * Search suppliers by name
   */
  loadSuppliers(): void {
    if (this.keySupplier && this.keySupplier.trim().length > 0) {
      this.supplierService.findByNameLike(this.keySupplier).subscribe(
        (data: Array<Supplier>) => {
          this.suppliers = data || [];
        },
        (error) => {
          this.notificationService.showError('Failed to search suppliers');
          console.error('Search suppliers error:', error);
        }
      );
    } else {
      this.findAll();
    }
  }

  /**
   * Set selected supplier from typeahead
   */
  setSelectedSupplier(event: any): void {
    if (event && event.item) {
      this.supplierDetail(event.item, -1);
    }
  }

  /**
   * Save supplier (create or update)
   */
  saveSupplier(): void {
    this.supplierService.save(this.supplier).subscribe(
      (result: any) => {
        this.responseHandler.handleSaveResponse(result, 'Supplier', () => {
          this.findAll();
          this.clear();
        });
      },
      (error) => {
        this.responseHandler.handleError(error, 'Save supplier');
      }
    );
  }

  /**
   * Handle supplier selection from table
   */
  supplierDetail(supplier: Supplier, index: number): void {
    this.supplier = { ...supplier }; // Create a copy to avoid direct mutation
    this.selectedRow = index;
  }

  /**
   * Clear form and reset selection
   */
  clear(): void {
    this.initSupplier();
    this.keySupplier = '';
  }

  /**
   * Handle pagination change
   */
  pageChanged(event: any): void {
    this.page = event.page;
    this.findAll();
  }

  /**
   * Closes the modal
   */
  closeModal() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

  /**
   * Sets the modalRef and updates the isModal flag
   * @param ref The modal reference
   */
  setModalRef(ref: BsModalRef) {
    this.modalRef = ref;
    this.isModal = true;
  }
}
