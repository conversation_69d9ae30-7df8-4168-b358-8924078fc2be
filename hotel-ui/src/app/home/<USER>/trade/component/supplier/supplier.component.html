<div class="container-fluid mt-3" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <!-- <PERSON><PERSON> Close Button (only for modal mode) -->
  <div *ngIf="isModal" class="d-flex justify-content-end mb-3">
    <button type="button" class="btn-close" aria-label="Close" (click)="closeModal()"></button>
  </div>

  <div class="row g-4">
    <!-- Suppliers List Section -->
    <div class="col-lg-8">
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Suppliers</h5>
            <div class="search-container">
              <div class="input-group">
                <span class="input-group-text bg-light border-end-0">
                  <i class="fas fa-search text-muted"></i>
                </span>
                <input [(ngModel)]="keySupplier"
                       [typeahead]="suppliers"
                       (typeaheadLoading)="loadSuppliers()"
                       (typeaheadOnSelect)="setSelectedSupplier($event)"
                       [typeaheadOptionsLimit]="7"
                       typeaheadWaitMs="1000"
                       typeaheadOptionField="name"
                       placeholder="Search suppliers..."
                       autocomplete="off"
                       class="form-control border-start-0"
                       name="supplierSearch">
              </div>
            </div>
          </div>
        </div>

        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th class="border-0 fw-semibold">Supplier Name</th>
                  <th class="border-0 fw-semibold text-center">Status</th>
                  <th class="border-0 fw-semibold text-center">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let supplier of suppliers; let i = index"
                    class="table-row"
                    [class.table-active]="i === selectedRow"
                    (click)="supplierDetail(supplier, i)">
                  <td class="align-middle">
                    <span class="entity-name">{{ supplier.name }}</span>
                  </td>
                  <td class="align-middle text-center">
                    <span class="badge" [class]="supplier.active ? 'badge-success' : 'badge-danger'">
                      <i class="fas" [class]="supplier.active ? 'fa-check-circle' : 'fa-times-circle'"></i>
                      {{ supplier.active ? 'Active' : 'Inactive' }}
                    </span>
                  </td>
                  <td class="align-middle text-center">
                    <button class="btn btn-sm btn-outline-primary"
                            (click)="supplierDetail(supplier, i); $event.stopPropagation()">
                      <i class="fas fa-edit"></i>
                    </button>
                  </td>
                </tr>
                <tr *ngIf="suppliers?.length === 0">
                  <td colspan="3" class="text-center py-4 text-muted">
                    <i class="fas fa-inbox fa-2x mb-3 d-block"></i>
                    No suppliers found
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div class="card-footer bg-white border-top" *ngIf="collectionSize > pageSize">
          <div class="d-flex justify-content-center">
            <pagination class="pagination-sm"
                        [totalItems]="collectionSize"
                        [(ngModel)]="page"
                        [boundaryLinks]="true"
                        [maxSize]="5"
                        (pageChanged)="pageChanged($event)">
            </pagination>
          </div>
        </div>
      </div>
    </div>
    <!-- Supplier Form Section -->
    <div class="col-lg-4">
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <h5 class="card-title mb-0">
            {{ supplier.id ? 'Edit Supplier' : 'Supplier Details' }}
          </h5>
        </div>

        <div class="card-body">
          <form #manageSupplierForm="ngForm" (ngSubmit)="saveSupplier()">

            <!-- Supplier Name Field -->
            <div class="form-group mb-3">
              <label class="form-label fw-semibold">Supplier Name</label>
              <input type="text"
                     class="form-control form-control-lg"
                     [(ngModel)]="supplier.name"
                     name="supplierName"
                     placeholder="Enter supplier name"
                     required>
            </div>

            <!-- Registration Number -->
            <div class="form-group mb-3">
              <label class="form-label fw-semibold">Registration Number</label>
              <input type="text"
                     class="form-control"
                     [(ngModel)]="supplier.regNo"
                     name="regNo"
                     placeholder="Registration number (optional)">
            </div>

            <!-- Address -->
            <div class="form-group mb-3">
              <label class="form-label fw-semibold">Address</label>
              <textarea class="form-control"
                        rows="2"
                        [(ngModel)]="supplier.address"
                        name="address"
                        placeholder="Enter address"></textarea>
            </div>

            <!-- Contact Information -->
            <div class="row">
              <div class="col-6">
                <div class="form-group mb-3">
                  <label class="form-label fw-semibold">Phone 1</label>
                  <input type="tel"
                         class="form-control"
                         [(ngModel)]="supplier.telephone1"
                         name="phone1"
                         placeholder="Primary phone"
                         pattern="^\d{10}$">
                </div>
              </div>
              <div class="col-6">
                <div class="form-group mb-3">
                  <label class="form-label fw-semibold">Phone 2</label>
                  <input type="tel"
                         class="form-control"
                         [(ngModel)]="supplier.telephone2"
                         name="phone2"
                         placeholder="Secondary phone"
                         pattern="^\d{10}$">
                </div>
              </div>
            </div>

            <!-- Email -->
            <div class="form-group mb-3">
              <label class="form-label fw-semibold">Email</label>
              <input type="email"
                     class="form-control"
                     [(ngModel)]="supplier.email"
                     name="email"
                     placeholder="Enter email address">
            </div>

            <!-- Contact Person -->
            <div class="row">
              <div class="col-6">
                <div class="form-group mb-3">
                  <label class="form-label fw-semibold">Contact Person</label>
                  <input type="text"
                         class="form-control"
                         [(ngModel)]="supplier.contactPersonName"
                         name="contactPerson"
                         placeholder="Contact person name">
                </div>
              </div>
              <div class="col-6">
                <div class="form-group mb-3">
                  <label class="form-label fw-semibold">Contact Phone</label>
                  <input type="tel"
                         class="form-control"
                         [(ngModel)]="supplier.contactPersonTelephone"
                         name="contactPhone"
                         placeholder="Contact phone"
                         pattern="^\d{10}$">
                </div>
              </div>
            </div>

            <!-- Balance -->
            <div class="form-group mb-4">
              <label class="form-label fw-semibold">Balance</label>
              <input type="number"
                     class="form-control"
                     [(ngModel)]="supplier.balance"
                     name="balance"
                     placeholder="Current balance"
                     step="0.01">
            </div>

            <!-- Status Toggle -->
            <div class="form-group mb-4">
              <label class="form-label fw-semibold mb-3">Supplier Settings</label>

              <div class="status-toggles">
                <div class="form-check form-switch">
                  <input class="form-check-input"
                         type="checkbox"
                         id="activeSwitch"
                         [(ngModel)]="supplier.active"
                         name="active">
                  <label class="form-check-label fw-semibold" for="activeSwitch">
                    Active Status
                  </label>
                  <div class="form-text">Enable this supplier for use</div>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="d-grid gap-2">
              <button type="submit" class="btn btn-primary btn-lg">
                {{ supplier.id ? 'Update Supplier' : 'Save Supplier' }}
              </button>
              <button type="button" class="btn btn-outline-secondary" (click)="clear()">
                Clear
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

