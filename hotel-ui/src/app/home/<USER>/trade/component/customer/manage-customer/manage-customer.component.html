<div class="container-fluid mt-3" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <!-- <PERSON><PERSON> <PERSON> Button (only for modal mode) -->
  <div *ngIf="isModal" class="d-flex justify-content-end mb-3">
    <button type="button" class="btn-close" aria-label="Close" (click)="modalRef.hide()"></button>
  </div>

  <div class="row g-4">
    <!-- Customers List Section -->
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <div class="d-flex justify-content-between align-items-center flex-wrap">
            <h5 class="card-title mb-0">Customers</h5>

            <!-- Search Filters -->
            <div class="search-filters d-flex gap-2 flex-wrap">
              <div class="search-container">
                <div class="input-group">
                  <span class="input-group-text bg-light border-end-0">
                    <i class="fas fa-user text-muted"></i>
                  </span>
                  <input [(ngModel)]="keyName"
                         [typeahead]="customerSearchList"
                         (typeaheadLoading)="loadCustomer()"
                         (typeaheadOnSelect)="setFilteredCustomer($event)"
                         [typeaheadOptionsLimit]="7"
                         typeaheadWaitMs="1000"
                         typeaheadOptionField="name"
                         placeholder="Search by name..."
                         autocomplete="off"
                         class="form-control border-start-0"
                         name="customerName">
                </div>
              </div>

              <div class="search-container">
                <div class="input-group">
                  <span class="input-group-text bg-light border-end-0">
                    <i class="fas fa-id-card text-muted"></i>
                  </span>
                  <input [(ngModel)]="keyNic"
                         [typeahead]="customerSearchList"
                         (typeaheadLoading)="loadCustomerByNic()"
                         (typeaheadOnSelect)="setFilteredCustomer($event)"
                         [typeaheadOptionsLimit]="7"
                         typeaheadWaitMs="1000"
                         typeaheadOptionField="nicBr"
                         placeholder="Search by NIC..."
                         autocomplete="off"
                         class="form-control border-start-0"
                         name="customerNic">
                </div>
              </div>

              <div class="search-container">
                <div class="input-group">
                  <span class="input-group-text bg-light border-end-0">
                    <i class="fas fa-phone text-muted"></i>
                  </span>
                  <input [(ngModel)]="keyTp"
                         [typeahead]="customerSearchList"
                         (typeaheadLoading)="loadCustomerByTp()"
                         (typeaheadOnSelect)="setFilteredCustomer($event)"
                         [typeaheadOptionsLimit]="7"
                         typeaheadWaitMs="1000"
                         typeaheadOptionField="telephone1"
                         placeholder="Search by phone..."
                         autocomplete="off"
                         class="form-control border-start-0"
                         name="customerPhone">
                </div>
              </div>

              <div class="form-check form-switch align-self-center">
                <input class="form-check-input"
                       type="checkbox"
                       id="activeFilter"
                       (change)="searchActiveResult($event)"
                       [value]="active"
                       [(ngModel)]="active">
                <label class="form-check-label" for="activeFilter">Active Only</label>
              </div>
            </div>
          </div>
        </div>

        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th class="border-0 fw-semibold">Customer Name</th>
                  <th class="border-0 fw-semibold d-none d-md-table-cell">NIC</th>
                  <th class="border-0 fw-semibold d-none d-md-table-cell">Phone</th>
                  <th class="border-0 fw-semibold d-none d-md-table-cell">Address</th>
                  <th class="border-0 fw-semibold d-none d-md-table-cell">Route</th>
                  <th class="border-0 fw-semibold text-center">Balance</th>
                  <th class="border-0 fw-semibold text-center">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let customer of customers; let i = index"
                    class="table-row"
                    [class.table-active]="i === selectedRow"
                    (click)="customerDetail(customer, i)">
                  <td class="align-middle">
                    <span class="entity-name">{{ customer.name }}</span>
                  </td>
                  <td class="align-middle d-none d-md-table-cell">
                    {{ customer.nicBr || 'N/A' }}
                  </td>
                  <td class="align-middle d-none d-md-table-cell">
                    {{ customer.telephone1 }}{{ customer.telephone2 ? ', ' + customer.telephone2 : '' }}
                  </td>
                  <td class="align-middle d-none d-md-table-cell">
                    {{ customer.address }}
                  </td>
                  <td class="align-middle d-none d-md-table-cell">
                    {{ customer.routeName || 'Not Assigned' }}
                  </td>
                  <td class="align-middle text-center">
                    <span class="badge" [class]="customer.balance >= 0 ? 'badge-success' : 'badge-danger'">
                      {{ customer.balance | number: '1.2-2' }}
                    </span>
                  </td>
                  <td class="align-middle text-center">
                    <div class="btn-group" role="group">
                      <button class="btn btn-sm btn-outline-primary"
                              (click)="openModal(true); $event.stopPropagation()"
                              [disabled]="selectedRow !== i">
                        <i class="fas fa-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary"
                              (click)="openModal(false); $event.stopPropagation()"
                              [disabled]="selectedRow !== i">
                        <i class="fas fa-edit"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                <tr *ngIf="customers?.length === 0">
                  <td colspan="7" class="text-center py-4 text-muted">
                    <i class="fas fa-users fa-2x mb-3 d-block"></i>
                    No customers found
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div class="card-footer bg-white border-top" *ngIf="collectionSize > 0">
          <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex gap-2">
              <button class="btn btn-secondary" type="button" (click)="openModal(true)"
                      [disabled]="selectedRow === null">
                View Customer
              </button>
              <button class="btn btn-primary ml-1" type="button" (click)="openModal(false)"
                      [disabled]="selectedRow === null">
                Edit Customer
              </button>
              <button class="btn btn-success ml-1" type="button" (click)="setSelectedCustomer()"
                      [hidden]="disableSetCustomer">
                Set Selected
              </button>
            </div>

            <pagination class="pagination-sm mb-0"
                        [totalItems]="collectionSize"
                        [(ngModel)]="page"
                        [boundaryLinks]="true"
                        [maxSize]="5"
                        (pageChanged)="pageChanged($event)">
            </pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>





