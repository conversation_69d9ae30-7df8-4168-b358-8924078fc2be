<div class="container-fluid mt-3 px-0 p-1">
  <!-- Professional Header Section -->
  <div class="professional-card mb-3 animate-fade-in">
    <div class="professional-card-header">
      <div *ngIf="!modalRef" class="d-flex justify-content-between align-items-center">
        <div>
          <h2 class="professional-card-title mb-0">{{ 'SITE_NAME' | translate }}</h2>
          <p class="text-professional-secondary mb-0">Sales Invoice Creation</p>
        </div>
        <div class="d-none d-md-flex gap-2">
          <button class="btn btn-professional btn-professional-outline btn-professional-outline-primary btn-professional-sm"
                  (click)="openCustomer()" title="Select Customer">
            <i class="fas fa-user me-1"></i>Customer
          </button>
          <button class="btn btn-professional btn-professional-outline btn-professional-outline-primary btn-professional-sm"
                  (click)="openStock()" title="Select Stock">
            <i class="fas fa-boxes me-1"></i>Stock
          </button>
          <button class="btn btn-professional btn-professional-outline btn-professional-outline-primary btn-professional-sm"
                  (click)="openPastInvoice()" title="Past Invoices">
            <i class="fas fa-history me-1"></i>History
          </button>
          <button class="btn btn-professional btn-professional-outline btn-professional-outline-primary btn-professional-sm"
                  (click)="openCashier()" title="Cashier">
            <i class="fas fa-cash-register me-1"></i>Cashier
          </button>
          <button class="btn btn-professional btn-professional-outline btn-professional-outline-secondary btn-professional-sm"
                  routerLink="../home/<USER>" (click)="closeFullscreen()" title="Home">
            <i class="fas fa-home me-1"></i>Home
          </button>
        </div>
      </div>

      <div *ngIf="modalRef" class="d-flex justify-content-between align-items-center">
        <div>
          <h2 class="professional-card-title mb-0">Edit Invoice {{ isUpdateMode ? '(Update Mode)' : '' }}</h2>
          <div *ngIf="isUpdateMode && si && si.invoiceNo" class="text-professional-secondary">
            <small>Invoice #: {{ si.invoiceNo }} | Customer: {{ si.customerName || 'N/A' }}</small>
          </div>
        </div>
        <div>
          <button type="button" class="btn btn-professional btn-professional-outline btn-professional-outline-secondary btn-professional-sm" (click)="close()">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>

      <!-- Mobile action buttons -->
      <div class="d-flex justify-content-between mt-3 d-md-none">
        <button class="btn btn-professional btn-professional-secondary btn-professional-sm px-2" (click)="openCustomer()">
          <i class="fas fa-user"></i>
        </button>
        <button class="btn btn-professional btn-professional-secondary btn-professional-sm px-2" (click)="openStock()">
          <i class="fas fa-boxes"></i>
        </button>
        <button class="btn btn-professional btn-professional-secondary btn-professional-sm px-2" (click)="openPastInvoice()">
          <i class="fas fa-history"></i>
        </button>
        <button class="btn btn-professional btn-professional-secondary btn-professional-sm px-2" (click)="openCashier()">
          <i class="fas fa-cash-register"></i>
        </button>
        <button class="btn btn-professional btn-professional-secondary btn-professional-sm px-2" routerLink="../home/<USER>" (click)="closeFullscreen()">
          <i class="fas fa-home"></i>
        </button>
      </div>
    </div>
  </div>
  <!-- Professional Item Search Section -->
  <div class="professional-card mb-3 animate-fade-in">
    <div class="professional-card-header">
      <h5 class="professional-card-title">Item Selection</h5>
    </div>
    <div class="professional-card-body">
      <div class="row g-3">
        <!-- Barcode field -->
        <div class="col-md-2 col-12">
          <div class="form-group-professional">
            <label class="form-label">{{ 'INVENTORY.BARCODE' | translate }}</label>
            <div class="input-group search-professional">
              <span class="input-group-text">
                <i class="fas fa-barcode text-professional-muted"></i>
              </span>
              <input [(ngModel)]="keyItemSearch"
                     [typeahead]="itemSearchList"
                     (typeaheadLoading)="searchItems()"
                     (typeaheadOnSelect)="setSelectedItem($event)"
                     [typeaheadOptionsLimit]="7"
                     typeaheadWaitMs="1000"
                     typeaheadOptionField="barcode"
                     autocomplete="off"
                     #barcodeEle (keydown.enter)="gotoPayment()"
                     class="form-control" name="searchItem"
                     placeholder="Scan or enter barcode">
              <button class="btn btn-professional btn-professional-outline btn-professional-outline-primary"
                      type="button" (click)="openBarcodeScanner()">
                <i class="fas fa-camera"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Item name field -->
        <div class="col-md-5 col-12">
          <div class="form-group-professional">
            <label class="form-label">Item Name</label>
            <div class="input-group search-professional">
              <span class="input-group-text">
                <i class="fas fa-search text-professional-muted"></i>
              </span>
              <input [(ngModel)]="keyItemNameSearch"
                     [typeahead]="itemNameSearchList"
                     (typeaheadLoading)="searchItemsByName()"
                     (typeaheadOnSelect)="setSelectedItem($event)"
                     [typeaheadOptionsLimit]="15"
                     typeaheadWaitMs="1000"
                     typeaheadOptionField="itemName"
                     autocomplete="off"
                     class="form-control" name="searchItem"
                     placeholder="Search by item name">
            </div>
          </div>
        </div>

        <!-- Price field -->
        <div class="col-md-2 col-4">
          <div class="form-group-professional">
            <div class="d-flex justify-content-between align-items-center">
              <label class="form-label">Price</label>
              <span class="badge badge-professional badge-professional-info">
                Stock: {{ availableQty }}
              </span>
            </div>
            <input type="number" required #price="ngModel" #sellingPrice
                   class="form-control" id="price" name="price"
                   [class.is-invalid]="price.invalid && price.touched"
                   [(ngModel)]="sPrice" (keydown.arrowRight)="focusDiscount()"
                   placeholder="0.00">
          </div>
        </div>

        <!-- Discount field -->
        <div class="col-md-1 col-4">
          <div class="form-group-professional">
            <label class="form-label d-flex align-items-center">
              Discount
              <button class="btn btn-sm ms-1 p-0" type="button" (click)="isPercentage = !isPercentage">
                <i class="fas" [class]="isPercentage ? 'fa-percent' : 'fa-minus'"
                   [title]="isPercentage ? 'Percentage' : 'Fixed Amount'"></i>
              </button>
            </label>
            <div class="position-relative">
              <input type="number" #discountForItm="ngModel" #discountForItem
                     class="form-control" id="discount" name="discount"
                     [(ngModel)]="discount" (keydown.arrowRight)="focusQty()"
                     placeholder="0">
              <span *ngIf="discount && sPrice" class="position-absolute text-professional-secondary"
                    style="right: 10px; top: 50%; transform: translateY(-50%); font-size: 0.75rem;">
                {{ isPercentage ? (sPrice - (sPrice * discount / 100)).toFixed(2) : (sPrice - discount).toFixed(2) }}
              </span>
            </div>
          </div>
        </div>

        <!-- Quantity field -->
        <div class="col-md-2 col-4">
          <div class="form-group-professional">
            <div class="d-flex justify-content-between align-items-center">
              <label class="form-label">Quantity</label>
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="returnSwitch"
                       [checked]="isReturn" (change)="isReturn = !isReturn">
                <label class="form-check-label small" for="returnSwitch">Return</label>
              </div>
            </div>
            <input type="number" required #qty="ngModel" #quantity
                   class="form-control" id="qty" name="qty"
                   [class.is-invalid]="qty.invalid && qty.touched"
                   [(ngModel)]="itemQty" (keydown.enter)="checkAvailability()"
                   (keydown.arrowLeft)="focusPrice()" placeholder="1">
          </div>
        </div>

        <!-- Add Item Button -->
        <div class="col-md-1 col-12 d-flex align-items-end">
          <button class="btn btn-professional btn-professional-success w-100 mb-3"
                  type="button" (click)="checkAvailability()">
            <i class="fas fa-plus me-1"></i>
            <span class="d-none d-md-inline">Add</span>
            <span class="d-md-none">Add Item</span>
          </button>
        </div>
      </div>
    </div>
  </div>
      </div>
      <div class="row p-0 m-0">
        <div class="col-md-12 p-0 m-0">
          <div class="table-height">
            <!-- Desktop table view -->
            <div class="table-responsive d-none d-md-block">
              <table class="table table-bordered">
                <thead>
                <tr>
                  <td style="width: 350px; !important;">Barcode</td>
                  <td>Item</td>
                  <td style="width: 150px; !important;">Qty</td>
                  <td style="width: 250px; !important;">Price</td>
                  <td style="width: 12px; !important;"></td>
                </tr>
                </thead>
                <tbody>
                <tr *ngFor="let siRec of si.salesInvoiceRecords,let i = index"
                    (click)="selectRow(i)" [class.active]="i === selectedSiRecordIndex">
                  <td>{{ siRec.barcode }}</td>
                  <td>{{ siRec.itemName }}</td>
                  <td>{{ siRec.quantity }}</td>
                  <td>{{ siRec.price | number }}</td>
                  <td>
                    <button class="btn btn-danger btn-sm" (click)="removeRow(i)">X</button>
                  </td>
                </tr>
                </tbody>
              </table>
            </div>

            <!-- Mobile card view -->
            <div class="d-md-none">
              <div class="card mb-2" *ngFor="let siRec of si.salesInvoiceRecords,let i = index"
                   (click)="selectRow(i)" [ngClass]="{'border-primary': i === selectedSiRecordIndex}">
                <div class="card-body p-2">
                  <div class="d-flex justify-content-between align-items-center">
                    <div>
                      <h6 class="mb-1">{{ siRec.itemName }}</h6>
                      <small class="text-muted">{{ siRec.barcode }}</small>
                    </div>
                    <div class="text-right">
                      <div><strong>{{ siRec.price | number }}</strong></div>
                      <div>Qty: {{ siRec.quantity }}</div>
                    </div>
                  </div>
                  <div class="text-right mt-2">
                    <button class="btn btn-danger btn-sm" (click)="removeRow(i)">Remove</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Payment info section - improved for mobile -->
      <div class="row mt-3 mx-0">
        <!-- Customer section -->
        <div class="col-12 col-md-3 mb-2 px-1">
          <label>Customer</label>
          <div class="input-group">
            <input [(ngModel)]="keyCustomerSearch"
                   [typeahead]="customerSearchList"
                   (typeaheadLoading)="searchCustomers()"
                   (typeaheadOnSelect)="setSelectedCustomer($event)"
                   [typeaheadOptionsLimit]="7"
                   typeaheadWaitMs="1000"
                   typeaheadOptionField="name"
                   autocomplete="off"
                   placeholder="Search customer"
                   class="form-control" name="searchCustomer">
            <div class="input-group-append">
              <button class="btn btn-primary" (click)="newCustomer()" type="button">
                <i class="fa fa-plus"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Totals section -->
        <div class="col-12 col-md-9 px-1">
          <div class="row mx-0">
            <div class="col-4 mb-2 px-1">
              <label>Sub Total</label>
              <input class="form-control" [ngModel]="si.subTotal | number" readonly>
            </div>
            <div class="col-4 mb-2 px-1">
              <label>Discount</label>
              <input class="form-control" [(ngModel)]="si.totalDiscount" (ngModelChange)="calculateTotal()">
            </div>
            <div class="col-4 mb-2 px-1">
              <label>Total</label>
              <input class="form-control" [ngModel]="si.totalAmount | number" readonly>
            </div>
          </div>
        </div>
      </div>

      <!-- Payment details section - improved for mobile -->
      <div class="row mt-2 mx-0">
        <div class="col-6 col-md-3 mb-2 px-1">
          <label>Reference</label>
          <input type="text" class="form-control" [(ngModel)]="si.reference" placeholder="Reference">
        </div>
        <div class="col-6 col-md-3 mb-2 px-1">
          <label>Payment</label>
          <input type="number" class="form-control" [(ngModel)]="si.payment" placeholder="Amount paying" #payment
                 (ngModelChange)="calculateBalance()" (keyup.enter)="saveByEnter()">
        </div>
        <div class="col-6 col-md-3 mb-2 px-1">
          <label>Payment Method</label>
          <select class="form-control" (change)="setPaymentMethod($event)" name="paymentMethodSelected"
                  [(ngModel)]="paymentMethodId" required #paymentMethodSelect="ngModel"
                  [class.is-invalid]="paymentMethodSelect.invalid && paymentMethodSelect.touched">
            <option>-Select-</option>
            <option *ngFor="let method of paymentMethods, let i = index"
                    [value]="method.id">
              {{ method.value }}
            </option>
          </select>
        </div>
        <div class="col-6 col-md-3 mb-2 px-1">
          <label>Balance</label>
          <input class="form-control" [ngModel]="si.cashBalance | number" readonly>
        </div>
      </div>
    </div>
    <!-- Action buttons - optimized for mobile -->
    <div class="mt-3">
      <!-- Desktop view buttons -->
      <div class="d-none d-md-flex justify-content-between align-items-center">
        <!-- Left aligned buttons -->
        <div>
          <button type="button" class="btn btn-primary mr-2" [disabled]="!isProcessing"
                  (click)="isProcessing = !isProcessing" title="Reset Controls">
            <i class="fa fa-sync-alt"></i> Reset Controls
          </button>
          <button type="button" class="btn btn-dark"
                  (click)="loadFromMemory()" title="Load Memory">
            <i class="fa fa-memory"></i> Load Memory
          </button>
        </div>

        <!-- Right aligned buttons -->
        <div>
          <button *ngIf="null != si.cheque" class="btn btn-outline-primary btn-lg mr-2" mwlConfirmationPopover
                  (confirm)="removeCheque()" [popoverMessage]="'Do you want to remove the Cheque ?'"
                  title="Remove Cheque">
            <i class="fa fa-money-check"></i> {{ 'Cheque - ' + si.cheque.chequeNo + ' - ' + si.cheque.chequeAmount + '  X' }}
          </button>
          <button type="button" class="btn btn-danger btn-lg mr-2" mwlConfirmationPopover (confirm)="clear()" title="Clear">
            <i class="fa fa-trash"></i> Clear
          </button>
          <!-- Show Save and Save & Print buttons only when not in update mode -->
          <ng-container *ngIf="!isUpdateMode">
            <button type="button" class="btn btn-primary btn-lg mr-2" (click)="save(false)" [disabled]="isProcessing"
                    title="Save">
              <i class="fa fa-save"></i> Save
            </button>
            <button type="button" class="btn btn-primary btn-lg" (click)="save(true)" [disabled]="isProcessing"
                    title="Save & Print">
              <i class="fa fa-print"></i> Save & Print
            </button>
          </ng-container>
          <!-- Show Update button only when in update mode -->
          <ng-container *ngIf="isUpdateMode">
            <button type="button" class="btn btn-warning btn-lg" (click)="updateInvoice()" [disabled]="isProcessing"
                    title="Update Invoice">
              <i class="fa fa-edit"></i> Update Invoice
            </button>
          </ng-container>
        </div>
      </div>

      <!-- Mobile view buttons - compact layout -->
      <div class="d-flex d-md-none justify-content-between">
        <div>
          <button type="button" class="btn btn-primary btn-sm mx-1" [disabled]="!isProcessing"
                  (click)="isProcessing = !isProcessing" title="Reset Controls">
            <i class="fa fa-sync-alt"></i>
          </button>
          <button type="button" class="btn btn-dark btn-sm mx-1"
                  (click)="loadFromMemory()" title="Load Memory">
            <i class="fa fa-memory"></i>
          </button>
          <button *ngIf="null != si.cheque" class="btn btn-outline-primary btn-sm mx-1" mwlConfirmationPopover
                  (confirm)="removeCheque()" [popoverMessage]="'Do you want to remove the Cheque ?'"
                  title="Remove Cheque">
            <i class="fa fa-money-check"></i>
          </button>
        </div>
        <div>
          <button type="button" class="btn btn-danger btn-sm mx-1" mwlConfirmationPopover (confirm)="clear()"
                  title="Clear">
            <i class="fa fa-trash"></i>
          </button>
          <!-- Show Save and Save & Print buttons only when not in update mode -->
          <ng-container *ngIf="!isUpdateMode">
            <button type="button" class="btn btn-primary btn-sm mx-1" (click)="save(false)" [disabled]="isProcessing"
                    title="Save">
              <i class="fa fa-save"></i>
            </button>
            <button type="button" class="btn btn-primary btn-sm mx-1" (click)="save(true)" [disabled]="isProcessing"
                    title="Save & Print">
              <i class="fa fa-print"></i>
            </button>
          </ng-container>
          <!-- Show Update button only when in update mode -->
          <ng-container *ngIf="isUpdateMode">
            <button type="button" class="btn btn-warning btn-sm mx-1" (click)="updateInvoice()" [disabled]="isProcessing"
                    title="Update Invoice">
              <i class="fa fa-edit"></i>
            </button>
          </ng-container>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #tempMultiplePrice>
  <div class="modal-header">
    <h5 class="modal-title">Select Price</h5>
    <button type="button" class="close" aria-label="Close" (click)="hidePricesModal()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body text-center">
    <div class="form-group col-md-12">
      <ul class="list-group">
        <li *ngFor="let pr of prices; let i = index" class="list-group-item list-group-item-action"
            (click)="setPrice(pr[1], pr[0])">
          {{ pr[1] + " - " + pr[0] }}
        </li>
      </ul>
    </div>
  </div>
</ng-template>




