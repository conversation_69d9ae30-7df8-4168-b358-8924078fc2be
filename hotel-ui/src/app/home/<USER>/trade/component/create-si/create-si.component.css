/* Import Professional Theme */
@import '../../../../../shared/styles/global-professional-theme.css';

/* Sales Invoice Specific Styles */
.table-height {
  height: 55vh;
  overflow-y: auto;
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-color);
}

/* Professional Table Styling */
.table-professional tbody tr.table-active {
  background-color: #e3f2fd !important;
  border-left: 4px solid #2196f3;
  color: var(--text-primary);
}

.table-professional tbody tr:hover {
  background-color: var(--bg-light) !important;
  transform: scale(1.01);
}

/* Professional Header Section */
.professional-card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.professional-card-header .professional-card-title {
  color: white;
}

.professional-card-header .text-professional-secondary {
  color: rgba(255, 255, 255, 0.8);
}

/* Item Search Section */
.search-professional {
  width: 100%;
}

.search-professional .input-group-text {
  background: var(--bg-light);
  border-color: var(--border-color);
}

/* Form Controls */
.form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Button Enhancements */
.btn-professional {
  min-height: 38px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
}

.btn-professional i {
  font-size: 0.9rem;
}

/* Professional Badge Styling */
.badge-professional {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .table-height {
    height: 40vh;
  }

  .professional-card-body {
    padding: 1rem;
  }

  .btn-professional {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}

/* Animation for smooth transitions */
.professional-card {
  transition: all 0.3s ease;
}

.professional-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Fix for horizontal overflow */
.container-fluid {
  max-width: 100%;
  overflow-x: hidden;
}

/* Discount field styling */
.position-relative .position-absolute {
  pointer-events: none;
  z-index: 1;
}

/* Form switch styling */
.form-check-input:checked {
  background-color: #667eea;
  border-color: #667eea;
}

/* Professional spacing */
.gap-2 {
  gap: 0.5rem;
}

.gap-3 {
  gap: 1rem;
}

/* Professional text colors */
.text-professional-primary {
  color: var(--text-primary);
}

.text-professional-secondary {
  color: var(--text-secondary);
}

.text-professional-muted {
  color: var(--text-muted);
}

/* Extra small buttons for inline editing */
.btn-xs {
  padding: 0.125rem 0.25rem;
  font-size: 0.75rem;
  line-height: 1.2;
  border-radius: 0.2rem;
  min-height: 24px;
  min-width: 30px;
}

.btn-xs i {
  margin-right: 0.25rem;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .form-group {
    padding-left: 5px;
    padding-right: 5px;
  }

  .form-control,
  input[type="text"],
  input[type="number"],
  select {
    font-size: 0.9rem;
    height: 38px !important;
  }

  .btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.85rem;
    min-width: 70px;
    height: 38px;
    min-height: 38px;
  }

  /* Smaller buttons for top and bottom action rows */
  .d-md-none .btn {
    padding: 0.25rem 0.4rem;
    font-size: 0.8rem;
    min-width: 40px;
  }

  /* Full width Add button in mobile view */
  .add-item-btn {
    margin-top: 0.5rem;
    margin-bottom: 1rem !important;
    font-weight: 600;
    font-size: 0.9rem !important;
    padding: 0.375rem !important;
    height: 38px !important;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    width: 100% !important; /* Ensure full width */
  }

  /* Adjust spacing for mobile action buttons */
  .d-md-none .mx-1 {
    margin-left: 0.15rem !important;
    margin-right: 0.15rem !important;
  }

  /* Ensure top navigation buttons fit in one row */
  .d-md-none .btn-sm.px-2 {
    padding-left: 0.4rem !important;
    padding-right: 0.4rem !important;
    min-width: auto;
  }

  /* Fix input group button alignment */
  .input-group-append .btn {
    height: 38px !important;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Ensure input and button heights match in input groups */
  .input-group .form-control,
  .input-group .input-group-append,
  .input-group .input-group-append .btn {
    height: 38px !important;
  }

  .table-height {
    height: 40vh;
  }

  /* Ensure buttons don't overflow */
  .d-flex.justify-content-between {
    flex-wrap: wrap;
  }

  /* Smaller margins between buttons */
  .btn {
    margin-right: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }

  /* Adjust input groups */
  .input-group {
    width: 100%;
  }

  /* Fix for card view in mobile */
  .card {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
  }
}
